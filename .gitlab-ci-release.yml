stages:
  - build
  - publish
  - pre-analysis
  - analysis
  - deploy

variables:
  DFD_IMAGE_DEV_PATH: "digifischdok-oci-dev.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-dev/digifischdok/${DFD_IMAGE_NAME}"
  DFD_IMAGE_PROD_PATH: "digifischdok-oci-prod.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-prod/digifischdok/${DFD_IMAGE_NAME}"

  # Deployment
  CI_STAGE_BRANCH: "stage"
  CI_TEST_BRANCH: "test"
  CI_PROD_BRANCH: "main"

  # Dataport Proxy for internet access
  DATAPORT_PROXY_HOST: zs-proxy.dataport.de
  DATAPORT_PROXY_PORT: 3128
  DATAPORT_PROXY: http://${DATAPORT_PROXY_HOST}:${DATAPORT_PROXY_PORT}
  HTTP_PROXY: $DATAPORT_PROXY
  HTTPS_PROXY: $DATAPORT_PROXY
  http_proxy: $DATAPORT_PROXY
  https_proxy: $DATAPORT_PROXY
  NO_PROXY: ".repo-ex.zcdi.dataport.de,.dataport.de,dsecurecloud.dsec.dataport.de,10.0.0.0/8"
  no_proxy: $NO_PROXY

.base-rules:
  rules:
    - &prevent_trigger_on_tags
      if: $CI_COMMIT_TAG
      when: never
    - &allow_manual_fail
      when: manual
      allow_failure: true

.base-application-release-rules:
  rules:
    # Release application on default, stage, test and prod branch
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
        || $CI_COMMIT_BRANCH == $CI_STAGE_BRANCH
        || $CI_COMMIT_BRANCH == $CI_TEST_BRANCH
        || $CI_COMMIT_BRANCH == $CI_PROD_BRANCH
      changes:
        - web-app/**/*
        - backend/register-service/**/*
    - *prevent_trigger_on_tags
    - *allow_manual_fail

.base-api-release-rules:
  rules:
    # Release API only on default branch
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - docs/architectural-documentation/src/docs/api-first/**/*
    - *prevent_trigger_on_tags
    - *allow_manual_fail

.base-web:
  variables:
    DFD_IMAGE_NAME: digifischdok-fachverfahren
    HELM_FOLDER_NAME: "web-app"
  before_script:
    - "export SUB_PROJECT_VERSION=$(sed -n 's/.*\"version\": \"\\(.*\\)\",/\\1/p' ${CI_PROJECT_DIR}/web-app/package.json)"
    - |
      if [ "$CI_COMMIT_BRANCH" == "$CI_PROD_BRANCH" ]; then
        export DFD_IMAGE_TAG=${SUB_PROJECT_VERSION}
      else
        export DFD_IMAGE_TAG=${SUB_PROJECT_VERSION}-${CI_COMMIT_SHORT_SHA}
      fi

.base-register-service:
  variables:
    DFD_IMAGE_NAME: digifischdok-register-service
    HELM_FOLDER_NAME: "register-service"
  before_script:
    - "export SUB_PROJECT_VERSION=$(sed -n \"/version =/s/.*'\\(.*\\)'.*/\\1/p\" ${CI_PROJECT_DIR}/backend/register-service/build.gradle)"
    - |
      if [ "$CI_COMMIT_BRANCH" == "$CI_PROD_BRANCH" ]; then
        export DFD_IMAGE_TAG=${SUB_PROJECT_VERSION}
      else
        export DFD_IMAGE_TAG=${SUB_PROJECT_VERSION}-${CI_COMMIT_SHORT_SHA}
      fi

.base-docker:
  image:
    name: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/kaniko-project/executor:debug
    entrypoint: [ "" ]
  before_script:
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        export DFD_IMAGE_PATH=${DFD_IMAGE_PROD_PATH}
      else
        export DFD_IMAGE_PATH=${DFD_IMAGE_DEV_PATH}
      fi
    - mkdir -p /kaniko/.docker
    - echo "${DOCKER_AUTH_CONFIG}" > /kaniko/.docker/config.json

# API-First
register-service-build-spec:
  stage: build
  extends: .base-api-release-rules
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/node:18-alpine
  script:
    - npm install -g @typespec/compiler
    - cd docs/architectural-documentation/src/docs/api-first
    - npm ci
    - npm run compile
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml
    expire_in: 1 day
  variables:
    TRIGGER_PATH: "docs/architectural-documentation/src/docs/api-first"

register-service-build-sdk-angular:
  stage: build
  extends: .base-api-release-rules
  needs: [ register-service-build-spec ]
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/openapitools/openapi-generator-cli:v7.10.0
  script:
    - cd docs/architectural-documentation/src/docs/api-first
    - "export SUB_PROJECT_VERSION=$(ls ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml | sed -E 's|^.*/openapi\\.Fischereiregister\\.([0-9]+\\.[0-9]+\\.[0-9]+(-snapshot)?)\\.yaml$|\\1|')"
    - mkdir -p ${CI_PROJECT_DIR}/openapi-angular
    - /usr/local/bin/docker-entrypoint.sh generate
      -g typescript-angular
      -i ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.${SUB_PROJECT_VERSION}.yaml
      -o ${CI_PROJECT_DIR}/openapi-angular
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/openapi-angular
    expire_in: 1 day
  variables:
    TRIGGER_PATH: "docs/architectural-documentation/src/docs/api-first"

register-service-publish-sdk-angular:
  stage: publish
  extends: .base-api-release-rules
  needs:
    - register-service-build-spec
    - register-service-build-sdk-angular
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/node:18-alpine
  script:
    - npm install -g @angular/cli
    - ng new library-workspace --no-create-application --skip-git
    - cd library-workspace
    - ng generate library @digifischdok/ngx-register-sdk
    - cd projects/digifischdok/ngx-register-sdk
    - "export SUB_PROJECT_VERSION=$(ls ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml | sed -E 's|^.*/openapi\\.Fischereiregister\\.([0-9]+\\.[0-9]+\\.[0-9]+)(-snapshot)?\\.yaml$|\\1|')"
    - npm version ${SUB_PROJECT_VERSION}
    - cp -r ${CI_PROJECT_DIR}/openapi-angular/* src/lib
    - echo "export * from './lib/index';" > src/public-api.ts
    - cd ../../../
    - ng build @digifischdok/ngx-register-sdk
    - cd dist/digifischdok/ngx-register-sdk
    - echo "registry=https://${DFD_NPM_ARTIFACTORY}" > .npmrc
    - echo "//${DFD_NPM_ARTIFACTORY}:_authToken=$DP_ARTIFACTORY_EX_TOKEN" >> .npmrc
    - npm publish --registry ${DFD_NPM_ARTIFACTORY}
  variables:
    TRIGGER_PATH: "docs/architectural-documentation/src/docs/api-first"

register-service-build-sdk-spring:
  stage: build
  extends: .base-api-release-rules
  needs: [ register-service-build-spec ]
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/openapitools/openapi-generator-cli:v7.10.0
  script:
    - cd docs/architectural-documentation/src/docs/api-first
    - "export SUB_PROJECT_VERSION=$(ls ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml | sed -E 's|^.*/openapi\\.Fischereiregister\\.([0-9]+\\.[0-9]+\\.[0-9]+(-snapshot)?)\\.yaml$|\\1|')"
    - mkdir -p ${CI_PROJECT_DIR}/openapi-spring
    - /usr/local/bin/docker-entrypoint.sh generate
      -g spring
      -i ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.${SUB_PROJECT_VERSION}.yaml
      -o ${CI_PROJECT_DIR}/openapi-spring
      -c spring.config.yaml
      -t templates/spring
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/openapi-spring
    expire_in: 1 day
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first"

register-service-publish-sdk-spring:
  stage: publish
  extends: .base-api-release-rules
  needs:
    - register-service-build-spec
    - register-service-build-sdk-spring
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/maven:3.9.9-eclipse-temurin-17
  script:
    - cd ${CI_PROJECT_DIR}/openapi-spring
    - cat ${ARTIFACTORY_MAVEN_SETTINGS} > settings.xml
    - sed -i '/<\/project>/e cat ${ARTIFACTORY_MAVEN_POM}' pom.xml
    - mvn deploy -s settings.xml -f pom.xml -X
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first"

web-update-package-lock:
  stage: publish
  extends:
    - .base-web
    - .base-application-release-rules
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/node:18-alpine
  script:
    - cd ${CI_PROJECT_DIR}/web-app
    - rm package-lock.json
    - rm .npmrc
    - echo "@digifischdok:registry=https://${DFD_NPM_ARTIFACTORY}" > .npmrc
    - echo "//${DFD_NPM_ARTIFACTORY}:_authToken=$DP_ARTIFACTORY_EX_TOKEN" >> .npmrc
    - echo "legacy-peer-deps=true" >> .npmrc
    - npm i --package-lock-only --production
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/web-app/package-lock.json
      - ${CI_PROJECT_DIR}/web-app/.npmrc
    expire_in: "1 hr"

web-docker-build-and-publish:
  needs:
    - job: web-update-package-lock
      artifacts: true
  stage: publish
  tags:
    - linux-medium-amd64
  extends:
    - .base-web
    - .base-docker
    - .base-application-release-rules
  before_script:
    - !reference [ .base-web, before_script ]
    - !reference [ .base-docker, before_script ]
  script:
    - echo ${DFD_IMAGE_TAG} > ${CI_PROJECT_DIR}/web-app-version.txt
    - /kaniko/executor
      --context ${CI_PROJECT_DIR}/web-app
      --dockerfile ${CI_PROJECT_DIR}/web-app/Dockerfile
      --destination ${DFD_IMAGE_PATH}:${DFD_IMAGE_TAG}
      --destination ${DFD_IMAGE_PATH}:latest
      --build-arg DOCKER_REGISTRY_URL="${DP_CMN_OCI_VIRT_ARTIFACTORY}"
      --build-arg HTTP_PROXY="${HTTP_PROXY}"
      --build-arg HTTPS_PROXY="${HTTPS_PROXY}"
      --build-arg NO_PROXY="${NO_PROXY}"
      --build-arg http_proxy="${HTTP_PROXY}"
      --build-arg https_proxy="${HTTPS_PROXY}"
      --build-arg no_proxy="${NO_PROXY}"
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/web-app-version.txt

register-service-docker-build-and-publish:
  stage: publish
  tags:
    - linux-medium-amd64
  extends:
    - .base-register-service
    - .base-docker
    - .base-application-release-rules
  before_script:
    - !reference [ .base-register-service, before_script ]
    - !reference [ .base-docker, before_script ]
  script:
    - echo ${DFD_IMAGE_TAG} > ${CI_PROJECT_DIR}/register-service-version.txt
    - /kaniko/executor
      --build-arg MAVEN_SDK_REGISTRY_USER_ID="${DP_ARTIFACTORY_USER_ID}"
      --build-arg MAVEN_SDK_REGISTRY_TOKEN="${DP_ARTIFACTORY_EX_TOKEN}"
      --build-arg DOCKER_REGISTRY_URL="${DP_CMN_OCI_VIRT_ARTIFACTORY}"
      --build-arg GRADLE_REGISTRY_USER_ID="${DP_ARTIFACTORY_USER_ID}"
      --build-arg GRADLE_REGISTRY_TOKEN="${DP_ARTIFACTORY_IN_TOKEN}"
      --build-arg HTTP_PROXY="${HTTP_PROXY}"
      --build-arg HTTPS_PROXY="${HTTPS_PROXY}"
      --build-arg NO_PROXY="${NO_PROXY}"
      --build-arg http_proxy="${HTTP_PROXY}"
      --build-arg https_proxy="${HTTPS_PROXY}"
      --build-arg no_proxy="${NO_PROXY}"
      --context ${CI_PROJECT_DIR}/backend/register-service
      --dockerfile ${CI_PROJECT_DIR}/backend/register-service/Dockerfile
      --destination ${DFD_IMAGE_PATH}:${DFD_IMAGE_TAG}
      --destination ${DFD_IMAGE_PATH}:latest
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/register-service-version.txt

.base-helm-publish:
  image:
    name: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/alpine/helm:3.16.3
    entrypoint: [ "" ]
  before_script:
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        export REGISTRY_URL=${DFD_OCI_PROD_ARTIFACTS}
      else
        export REGISTRY_URL=${DFD_OCI_DEV_ARTIFACTS}
      fi
    - echo ${DP_ARTIFACTORY_EX_TOKEN} | helm registry login ${REGISTRY_URL} --username ${DP_ARTIFACTORY_USER_ID} --password-stdin
    - cd ${CI_PROJECT_DIR}/deployment/kubernetes/${HELM_FOLDER_NAME}
    - helm registry login ${REGISTRY_URL} --username ${DP_ARTIFACTORY_USER_ID} --password ${DP_ARTIFACTORY_EX_TOKEN}
    - helm dependency update
    - helm dependency build

web-helm-publish:
  stage: publish
  needs:
    - web-docker-build-and-publish
  extends:
    - .base-web
    - .base-helm-publish
    - .base-application-release-rules
  before_script:
    - !reference [ .base-web, before_script ]
    - !reference [ .base-helm-publish, before_script ]
  script:
    - cd ${CI_PROJECT_DIR}/deployment/kubernetes
    - yq -i ".image.tag = \"$(cat ${CI_PROJECT_DIR}/web-app-version.txt)\"" ./${HELM_FOLDER_NAME}/values.yaml
    - helm package web-app
    - helm push web-app-$(cat web-app/Chart.yaml | yq '.version').tgz oci://${REGISTRY_URL}

register-service-helm-publish:
  stage: publish
  needs:
    - register-service-docker-build-and-publish
  extends:
    - .base-register-service
    - .base-helm-publish
    - .base-application-release-rules
  before_script:
    - !reference [ .base-register-service, before_script ]
    - !reference [ .base-helm-publish, before_script ]
  script:
    - cd ${CI_PROJECT_DIR}/deployment/kubernetes
    - yq -i ".image.tag = \"$(cat ${CI_PROJECT_DIR}/register-service-version.txt)\"" ./${HELM_FOLDER_NAME}/values.yaml
    - helm package register-service
    - helm push register-service-$(cat register-service/Chart.yaml | yq '.version').tgz oci://${REGISTRY_URL}

umbrella-helm-publish:
  stage: publish
  extends:
    - .base-helm-publish
    - .base-application-release-rules
  needs:
    - web-helm-publish
    - register-service-helm-publish
    - web-docker-build-and-publish
    - register-service-docker-build-and-publish
  image:
    name: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/alpine/helm:3.16.3
    entrypoint: [ "" ]
  before_script:
    - apk update && apk add yq
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        yq -i ".dependencies[].repository = \"oci://${DFD_OCI_PROD_ARTIFACTS}\"" ${CI_PROJECT_DIR}/deployment/kubernetes/digifischdok/Chart.yaml
      fi
    - !reference [ .base-helm-publish, before_script ]
    # Determine the correct VALUES_YAML based on the branch
    - >
      if [ "$CI_COMMIT_BRANCH" == "$CI_DEFAULT_BRANCH" ]; then
        export VALUES_YAML="values-dev.yaml"
      elif [ "$CI_COMMIT_BRANCH" == "$CI_STAGE_BRANCH" ]; then
        export VALUES_YAML="values-stage.yaml"
      elif [ "$CI_COMMIT_BRANCH" == "$CI_TEST_BRANCH" ]; then
        export VALUES_YAML="values-test.yaml"
      elif [ "$CI_COMMIT_BRANCH" == "$CI_PROD_BRANCH" ]; then
        export VALUES_YAML="values-rz.yaml"
      else
        echo "Branch $CI_COMMIT_BRANCH is not a valid branch for deployment"
      fi
    - cd ${CI_PROJECT_DIR}/deployment/kubernetes/${HELM_FOLDER_NAME}
    - yq -i ".web-app.image.tag = \"$(cat ${CI_PROJECT_DIR}/web-app-version.txt)\"" ${VALUES_YAML}
    - yq -i ".register-service.image.tag = \"$(cat ${CI_PROJECT_DIR}/register-service-version.txt)\"" ${VALUES_YAML}
  script:
    - cd ${CI_PROJECT_DIR}/deployment/kubernetes
    - helm package digifischdok
    - helm push digifischdok-$(cat digifischdok/Chart.yaml | yq '.version').tgz oci://${REGISTRY_URL}
    - mkdir -p kyverno
    - helm template digifischdok --values digifischdok/${VALUES_YAML} > kyverno/manifests.yaml
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/deployment/kubernetes/kyverno/manifests.yaml
      - ${CI_PROJECT_DIR}/deployment/kubernetes/digifischdok/${VALUES_YAML}
    expire_in: 4 weeks
  variables:
    HELM_FOLDER_NAME: "digifischdok"

register-service-build:
  stage: pre-analysis
  tags:
    - linux-medium-amd64
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/gradle:8.12-jdk21-alpine
  extends:
    - .base-register-service
    - .base-application-release-rules
  dependencies: [ ] # ignore artifacts
  variables:
    GRADLE_USER_HOME: '${CI_PROJECT_DIR}/.gradle'
    GRADLE_REGISTRY_USER_ID: ${DP_ARTIFACTORY_USER_ID}
    GRADLE_REGISTRY_TOKEN: ${DP_ARTIFACTORY_IN_TOKEN}
    MAVEN_SDK_REGISTRY_USER_ID: ${DP_ARTIFACTORY_USER_ID}
    MAVEN_SDK_REGISTRY_TOKEN: ${DP_ARTIFACTORY_EX_TOKEN}
    HTTP_PROXY: $DATAPORT_PROXY
    HTTPS_PROXY: $DATAPORT_PROXY
    http_proxy: $DATAPORT_PROXY
    https_proxy: $DATAPORT_PROXY
    NO_PROXY: $NO_PROXY
    no_proxy: $NO_PROXY
  script:
    - cd backend/register-service
    - gradle build -x test
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/.gradle/caches/modules-2/files-2.1/**/**/*.jar
      - backend/**/build/classes/java/main

register-service-unit-test:
  stage: pre-analysis
  image: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/gradle:8.12-jdk21-alpine
  extends:
    - .base-register-service
    - .base-application-release-rules
  variables:
    GRADLE_REGISTRY_USER_ID: ${DP_ARTIFACTORY_USER_ID}
    GRADLE_REGISTRY_TOKEN: ${DP_ARTIFACTORY_IN_TOKEN}
    MAVEN_SDK_REGISTRY_USER_ID: ${DP_ARTIFACTORY_USER_ID}
    MAVEN_SDK_REGISTRY_TOKEN: ${DP_ARTIFACTORY_EX_TOKEN}
    HTTP_PROXY: $DATAPORT_PROXY
    HTTPS_PROXY: $DATAPORT_PROXY
    http_proxy: $DATAPORT_PROXY
    https_proxy: $DATAPORT_PROXY
    NO_PROXY: $NO_PROXY
    no_proxy: $NO_PROXY
  dependencies: [ ] # ignore artifacts
  script:
    - cd backend/register-service
    - gradle unitTest jacocoFullReport --continue
  artifacts:
    expire_in: 4 weeks
    reports:
      junit: backend/register-service/**/build/test-results/unitTest/**/TEST-*.xml
      coverage_report:
        coverage_format: jacoco
        path: backend/register-service/build/reports/jacoco/jacocoFullReport/jacocoFullReport.xml
    paths:
      - backend/register-service/build/reports/jacoco/jacocoFullReport/jacocoFullReport.xml

run-sonarqube-scan:
  stage: analysis
  extends:
    - .base-application-release-rules
  allow_failure: true
  image:
    name: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/sonarsource/sonar-scanner-cli:11.1
    entrypoint: [ '' ]
  needs:
    - register-service-build
    - register-service-unit-test
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar'
    GIT_DEPTH: '0'
    SONAR_HOST_URL: ${DFD_SONAR_HOST_URL}
    SONAR_PROJEKT_KEY: ${DFD_SONAR_PROJEKT_KEY}
    SONAR_TOKEN: ${DFD_SONAR_TOKEN}
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
      -Dsonar.projectKey=DigiFischDok
      -Dsonar.java.libraries=${CI_PROJECT_DIR}/.gradle/caches/modules-2/files-2.1/**/**/*.jar
      -Dsonar.qualitygate.wait=true
      -Dsonar.qualitygate.timeout=1000
      -Dsonar.java.binaries=backend/**/build/classes/java/main
      -Dsonar.eslint.reportPaths=web-app/lint-report.json
      -Dsonar.exclusions=backend/**/src/test/java/**/*,backend/register-service/migrations/**/*,mobile-app/**/*,web-app/cypress/**,
      -Dsonar.test.inclusions=backend/**/src/test/java/**/*.java
      -Dsonar.coverage.exclusions=**/*.ts,**/*.js,**/*.html,**/*Dto.java,backend/**/import_testdata/**/*,backend/*/src/test/**,backend/**/observability/**/*,**/*Controller.java,docs/**,backend/register-service/migrations/**/*,backend/**/domain/**/*,backend/**/security/model/**/*,**/*Command.java,**/*Event.java,**/*Error.java,**/*Exception.java,**/*Request.java,**/*Config.java,**/*Configuration.java,**/order/OrderServiceImpl.java,**/order/OrderProperties.java,**/*Converter.java,**/*Repository.java,**/*View.java,**/*Properties.java,**/enums/**/*.java,**/type/**/*.java,**/data/**/*,**/model/**/*
      -Dsonar.coverage.jacoco.xmlReportPaths=backend/register-service/build/reports/jacoco/jacocoFullReport/jacocoFullReport.xml
      -Dsonar.cpd.exclusions=web-app/src/app/shared/icons/**,**/*Test.java,web-app/cypress/**
      -Dsonar.issue.ignore.multicriteria=e1,e2
      -Dsonar.issue.ignore.multicriteria.e1.ruleKey=java:S112
      -Dsonar.issue.ignore.multicriteria.e1.resourceKey=**/*
      -Dsonar.issue.ignore.multicriteria.e2.ruleKey=java:S1452
      -Dsonar.issue.ignore.multicriteria.e2.resourceKey=**/*Controller.java

run-trivy-scan-web-app:
  stage: analysis
  extends:
    - .base-web
    - .base-application-release-rules
  needs:
    - web-docker-build-and-publish
  allow_failure: true
  image:
    name: ${DP_IMAGES_ARTIFACTS}/trivy
    entrypoint: [ "" ]
  before_script:
    - !reference [ .base-web, before_script ]
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        export DFD_IMAGE_PATH=${DFD_IMAGE_PROD_PATH}
      else
        export DFD_IMAGE_PATH=${DFD_IMAGE_DEV_PATH}
      fi
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        export REGISTRY_URL=${DFD_OCI_PROD_ARTIFACTS}
      else
        export REGISTRY_URL=${DFD_OCI_DEV_ARTIFACTS}
      fi
    - mkdir -p ${HOME}/.docker
    - echo "{\"auths\":{\"${REGISTRY_URL}\":{\"auth\":\"${DFD_OCI_ARTIFACTS_AUTH}\"}}}" > ${HOME}/.docker/config.json
    - ls -lasi
    - > # Vulnerability DB Download
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --download-db-only
      --no-progress
      --cache-dir .trivycache/
      --insecure
      --db-repository ${DP_CMN_OCI_VIRT_ARTIFACTORY}/aquasecurity/trivy-db:2
    - > # JAVA Vulnerability DB Download
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --download-java-db-only
      --no-progress
      --cache-dir .trivycache/
      --insecure
      --java-db-repository ${DP_CMN_OCI_VIRT_ARTIFACTORY}/aquasecurity/trivy-java-db:1
  script:
    - > # JSON-Output
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --skip-db-update
      --skip-java-db-update
      --no-progress
      --cache-dir .trivycache/
      --list-all-pkgs
      --format json
      --report all
      --output "TRIVY-${CI_PROJECT_NAME}-${CI_COMMIT_SHA}.scan.json"
      ${DFD_IMAGE_PATH}:${DFD_IMAGE_TAG}
    - > # TEXT-Output
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --skip-db-update
      --skip-java-db-update
      --no-progress
      --cache-dir .trivycache/
      --format table
      --report all
      --output "TRIVY-${CI_PROJECT_NAME}-${CI_COMMIT_SHA}.scan.txt"
      ${DFD_IMAGE_PATH}:${DFD_IMAGE_TAG}
  cache:
    paths:
      - .trivycache/
  artifacts:
    when: always
    paths:
      - "TRIVY-*.scan.txt"
      - "TRIVY-*.scan.json"
    expire_in: 4 weeks

run-trivy-scan-register-service:
  stage: analysis
  extends:
    - .base-register-service
    - .base-application-release-rules
  needs:
    - register-service-docker-build-and-publish
  image:
    name: ${DP_IMAGES_ARTIFACTS}/trivy
    entrypoint: [ "" ]
  before_script:
    - !reference [ .base-register-service, before_script ]
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        export DFD_IMAGE_PATH=${DFD_IMAGE_PROD_PATH}
      else
        export DFD_IMAGE_PATH=${DFD_IMAGE_DEV_PATH}
      fi
    - mkdir -p ${HOME}/.docker
    - >
      if [ $CI_COMMIT_BRANCH == $CI_PROD_BRANCH ]; then
        export REGISTRY_URL=${DFD_OCI_PROD_ARTIFACTS}
      else
        export REGISTRY_URL=${DFD_OCI_DEV_ARTIFACTS}
      fi
    - echo "{\"auths\":{\"${REGISTRY_URL}\":{\"auth\":\"${DFD_OCI_ARTIFACTS_AUTH}\"}}}" > ${HOME}/.docker/config.json
    - ls -lasi
    - > # Vulnerability DB Download
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --download-db-only
      --no-progress
      --cache-dir .trivycache/
      --insecure
      --db-repository ${DP_CMN_OCI_VIRT_ARTIFACTORY}/aquasecurity/trivy-db:2
    - > # JAVA Vulnerability DB Download
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --download-java-db-only
      --no-progress
      --cache-dir .trivycache/
      --insecure
      --java-db-repository ${DP_CMN_OCI_VIRT_ARTIFACTORY}/aquasecurity/trivy-java-db:1
  script:
    - > # JSON-Output
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --skip-db-update
      --skip-java-db-update
      --no-progress
      --cache-dir .trivycache/
      --list-all-pkgs
      --format json
      --report all
      --output "TRIVY-${CI_PROJECT_NAME}-${CI_COMMIT_SHA}.scan.json"
      ${DFD_IMAGE_PATH}:${DFD_IMAGE_TAG}
    - > # TEXT-Output
      trivy image
      --username ${DP_ARTIFACTORY_USER_ID}
      --password ${DP_ARTIFACTORY_EX_TOKEN}
      --skip-db-update
      --skip-java-db-update
      --no-progress
      --cache-dir .trivycache/
      --format table
      --report all
      --output "TRIVY-${CI_PROJECT_NAME}-${CI_COMMIT_SHA}.scan.txt"
      ${DFD_IMAGE_PATH}:${DFD_IMAGE_TAG}
  cache:
    paths:
      - .trivycache/
  artifacts:
    when: always
    paths:
      - "TRIVY-*.scan.txt"
      - "TRIVY-*.scan.json"
    expire_in: 4 weeks

run-kyverno-scan:
  extends: .base-application-release-rules
  stage: analysis
  image: ${DP_IMAGES_ARTIFACTS}/kyvernocli
  allow_failure: true
  needs:
    - umbrella-helm-publish
  before_script:
    - mkdir -p public/digifischdok
  script:
    - >
      if [ ! -f "${CI_PROJECT_DIR}/deployment/kubernetes/kyverno/manifests.yaml" ] ; then
        echo "KYVERNO_SCAN_DEPLOY_FILE kyverno/manifests.yaml not found. Skipping Kyverno scan..." | tee ${CI_PROJECT_DIR}/public/digifischdok/kyverno.txt
      else
        kyverno apply /kyverno/policies/base --resource "${CI_PROJECT_DIR}/deployment/kubernetes/kyverno/manifests.yaml" --table --audit-warn | tee ${CI_PROJECT_DIR}/public/digifischdok/kyverno.scan.txt
        kyverno apply /kyverno/policies/base --resource "${CI_PROJECT_DIR}/deployment/kubernetes/kyverno/manifests.yaml" --audit-warn --policy-report | tee ${CI_PROJECT_DIR}/public/digifischdok/kyverno.scan.yaml
      fi
  artifacts:
    when: always
    paths:
      - ${CI_PROJECT_DIR}/public/digifischdok/kyverno.scan.txt
      - ${CI_PROJECT_DIR}/public/digifischdok/kyverno.scan.yaml
    expire_in: 4 weeks

helm-deploy:
  stage: deploy
  tags:
    - linux-medium-amd64
  rules:
    # Deploy application only on default, stage and test branch
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
        || $CI_COMMIT_BRANCH == $CI_STAGE_BRANCH
        || $CI_COMMIT_BRANCH == $CI_TEST_BRANCH
      changes:
        - web-app/**/*
        - backend/register-service/**/*
    - *prevent_trigger_on_tags
    - *allow_manual_fail
  needs:
    - web-docker-build-and-publish
    - register-service-docker-build-and-publish
    - umbrella-helm-publish
  image:
    name: ${DP_CMN_OCI_VIRT_ARTIFACTORY}/alpine/helm:3.16.3
    entrypoint: [ "" ]
  before_script:
    # Determine the correct KUBECONFIG_FILE and VALUES_YAML based on the branch
    - >
      if [ "$CI_COMMIT_BRANCH" == "$CI_DEFAULT_BRANCH" ]; then
        export KUBECONFIG_FILE=$KUBE_CONFIG_DEV
        export VALUES_YAML="values-dev.yaml"
      elif [ "$CI_COMMIT_BRANCH" == "$CI_STAGE_BRANCH" ]; then
        export KUBECONFIG_FILE=$KUBE_CONFIG_STAGE
        export VALUES_YAML="values-stage.yaml"
      elif [ "$CI_COMMIT_BRANCH" == "$CI_TEST_BRANCH" ]; then
        export KUBECONFIG_FILE=$KUBE_CONFIG_TEST
        export VALUES_YAML="values-test.yaml"
      else
        echo "Branch $CI_COMMIT_BRANCH is not a valid branch for deployment"
      fi
    - apk update && apk add kubectl envsubst
    - mkdir -p $HOME/.kube
    - echo "$KUBECONFIG_FILE" > $HOME/.kube/config
    - export KUBECONFIG="$HOME/.kube/config"
    - kubectl config set-context --current --namespace=default
    - echo ${DP_ARTIFACTORY_EX_TOKEN} | helm registry login ${DFD_OCI_DEV_ARTIFACTS} --username ${DP_ARTIFACTORY_USER_ID} --password-stdin
    - kubectl get secret dfd-oci-registry || kubectl create secret docker-registry dfd-oci-registry
      --docker-server=${DFD_OCI_DEV_ARTIFACTS}
      --docker-username=${DP_ARTIFACTORY_USER_ID}
      --docker-password=${DP_ARTIFACTORY_EX_TOKEN}
  script:
    - envsubst < ${CI_PROJECT_DIR}/deployment/kubernetes/digifischdok/${VALUES_YAML} > ${CI_PROJECT_DIR}/deployment/kubernetes/digifischdok/values-filled.yaml
    - helm upgrade --install digifischdok oci://${DFD_OCI_DEV_ARTIFACTS}/digifischdok
      -f ${CI_PROJECT_DIR}/deployment/kubernetes/digifischdok/values-filled.yaml