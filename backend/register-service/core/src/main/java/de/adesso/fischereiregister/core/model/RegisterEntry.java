package de.adesso.fischereiregister.core.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import de.adesso.fischereiregister.core.authorization.AccessChecker;
import de.adesso.fischereiregister.core.commands.BanPermanentlyCommand;
import de.adesso.fischereiregister.core.commands.BanTemporarilyCommand;
import de.adesso.fischereiregister.core.commands.ChangePersonalDataCommand;
import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.commands.CreatePersonCommand;
import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.ESCreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.ExtendLicenseCommand;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.commands.OSCreatePersonCommand;
import de.adesso.fischereiregister.core.commands.OSCreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSCreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSExtendFishingLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.commands.OrderReplacementCardCommand;
import de.adesso.fischereiregister.core.commands.PayFishingTaxCommand;
import de.adesso.fischereiregister.core.commands.UnbanCommand;
import de.adesso.fischereiregister.core.commands.results.BanCommandResult;
import de.adesso.fischereiregister.core.commands.results.CertificateCommandResult;
import de.adesso.fischereiregister.core.commands.results.ChangePersonCommandResult;
import de.adesso.fischereiregister.core.commands.results.CommandResult;
import de.adesso.fischereiregister.core.commands.results.DigitizeRegularLicenseCommandResult;
import de.adesso.fischereiregister.core.commands.results.FishingTaxCommandResult;
import de.adesso.fischereiregister.core.commands.results.JurisdictionCommandResult;
import de.adesso.fischereiregister.core.commands.results.LicenseCommandResult;
import de.adesso.fischereiregister.core.commands.results.PersonCommandResult;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.exceptions.FederalStateMismatchException;
import de.adesso.fischereiregister.core.exceptions.JurisdictionAlreadyAssignedException;
import de.adesso.fischereiregister.core.exceptions.JurisdictionMismatchException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.TaxInWrongJurisdictionException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.processors.BanPermanentlyCommandProcessor;
import de.adesso.fischereiregister.core.processors.BanTemporarilyCommandProcessor;
import de.adesso.fischereiregister.core.processors.ChangePersonalDataCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateFishingCertificateCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateLimitedLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreatePersonCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateRegularLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateVacationLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.DigitizeRegularLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.ESCreateFishingCertificateCommandProcessor;
import de.adesso.fischereiregister.core.processors.ExtendLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.MoveJurisdictionCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreatePersonCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreateRegularLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreateVacationLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSExtendFishingLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSPayTaxCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSReplacementCardOrderedCommandProcessor;
import de.adesso.fischereiregister.core.processors.OrderReplacementCardCommandProcessor;
import de.adesso.fischereiregister.core.processors.PayFishingTaxCommandProcessor;
import de.adesso.fischereiregister.core.processors.UnbanCommandProcessor;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import de.adesso.fischereiregister.core.validation.BanPermanentlyCommandValidator;
import de.adesso.fischereiregister.core.validation.BanTemporarilyCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateFishingCertificateCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateLimitedLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.CreatePersonCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateRegularLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateVacationLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.DigitizeRegularLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.ESCreateFishingCertificateCommandValidator;
import de.adesso.fischereiregister.core.validation.ExtendLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.MoveJurisdictionCommandValidator;
import de.adesso.fischereiregister.core.validation.OSCreateRegularLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.OSCreateVacationLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.OSExtendFishingLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.OSReplaceCardCommandValidator;
import de.adesso.fischereiregister.core.validation.OSTaxCommandValidator;
import de.adesso.fischereiregister.core.validation.OrderReplacementCardCommandValidator;
import de.adesso.fischereiregister.core.validation.PayFishingTaxCommandValidator;
import lombok.Getter;
import lombok.Setter;
import org.axonframework.commandhandling.CommandHandler;
import org.axonframework.eventsourcing.EventSourcingHandler;
import org.axonframework.modelling.command.AggregateCreationPolicy;
import org.axonframework.modelling.command.AggregateIdentifier;
import org.axonframework.modelling.command.AggregateLifecycle;
import org.axonframework.modelling.command.CreationPolicy;
import org.axonframework.spring.stereotype.Aggregate;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@SuppressWarnings("unused")
@Getter
@Setter
@Aggregate
@JsonIgnoreProperties(ignoreUnknown = true)
public class RegisterEntry {

    @AggregateIdentifier
    private UUID registerId;

    private Person person;
    private Jurisdiction jurisdiction;
    private Ban ban;

    private final List<FishingLicense> fishingLicenses = new ArrayList<>();
    private final List<QualificationsProof> qualificationsProofs = new ArrayList<>();
    private final List<Fee> fees = new ArrayList<>();
    private final List<Tax> taxes = new ArrayList<>();
    private final List<IdentificationDocument> identificationDocuments = new ArrayList<>();

    private String serviceAccountId;
    private String inboxReference;

    public RegisterEntry() {
        // This empty constructor is required for Axon to work
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.ALWAYS)
    public CommandResult handleDigitizeRegularLicenseCommand(DigitizeRegularLicenseCommand command, DigitizeRegularLicenseCommandValidator validator, DigitizeRegularLicenseCommandProcessor processor) throws RulesProcessingException {
        final List<Tax> combinedTaxes = new ArrayList<>();
        combinedTaxes.addAll(command.taxes());
        combinedTaxes.addAll(command.payedTaxes());

        ensureCommandTaxesMatchUserJurisdiction(combinedTaxes, command.userDetails());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final RegularLicenseDigitizedEvent event = (RegularLicenseDigitizedEvent) events.get(0);

        return DigitizeRegularLicenseCommandResult.builder()
                .documents(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .person(event.person())
                .jurisdiction(event.jurisdiction())
                .registerEntryId(command.registerId())
                .build();
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.ALWAYS)
    public CertificateCommandResult handleCreateFishingCertificateCommand(
            CreateFishingCertificateCommand command,
            CreateFishingCertificateCommandValidator validator,
            CreateFishingCertificateCommandProcessor processor) throws RulesProcessingException {

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final QualificationsProofCreatedEvent event = (QualificationsProofCreatedEvent) events.get(0);

        return CertificateCommandResult.builder()
                .registerEntryId(this.registerId)
                .fishingCertificateId(event.qualificationsProof().getFishingCertificateId())
                .build();
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.ALWAYS)
    public CertificateCommandResult handleESCreateFishingCertificateCommand(ESCreateFishingCertificateCommand command, ESCreateFishingCertificateCommandValidator validator, ESCreateFishingCertificateCommandProcessor processor) {
        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final QualificationsProofCreatedEvent event = (QualificationsProofCreatedEvent) events.get(0);

        return CertificateCommandResult.builder()
                .registerEntryId(this.registerId)
                .fishingCertificateId(event.qualificationsProof().getFishingCertificateId())
                .build();
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.ALWAYS)
    public CommandResult handleCreatePersonCommand(CreatePersonCommand command, CreatePersonCommandValidator validator, CreatePersonCommandProcessor processor) throws RulesProcessingException {
        final List<Tax> combinedTaxes = new ArrayList<>();
        combinedTaxes.addAll(command.taxes());
        combinedTaxes.addAll(command.payedTaxes());

        ensureCommandTaxesMatchUserJurisdiction(combinedTaxes, command.userDetails());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final PersonCreatedEvent event = (PersonCreatedEvent) events.get(0);

        return PersonCommandResult.builder()
                .documents(event.identificationDocuments())
                .person(event.person())
                .registerEntryId(this.registerId)
                .build();
    }

    /**
     * Online Service users do not have a federal state and can create taxes for every federal state
     * so following is not needed: if (AccessChecker.commandTaxesMatchUserJurisdiction...
     *
     * @param command
     * @param validator
     * @param processor
     */
    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.ALWAYS)
    public void handleOSCreatePersonCommand(OSCreatePersonCommand command, OSTaxCommandValidator validator, OSCreatePersonCommandProcessor processor) {
        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.CREATE_IF_MISSING)
    public LicenseCommandResult handleCreateLimitedLicenseCommand(CreateLimitedLicenseCommand command, CreateLimitedLicenseCommandValidator validator, CreateLimitedLicenseCommandProcessor processor) {
        if(this.registerId != null) { // if the register entry already exists, we need to check if the jurisdiction matches
            ensureUserJurisdictionMatches(command.userDetails());
        }

        validator.validateOrThrow(command,this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final LimitedLicenseCreatedEvent event = (LimitedLicenseCreatedEvent) events.get(0);

        return LicenseCommandResult.builder()
                .fishingLicense(event.fishingLicense())
                .person(event.person())
                .documents(event.identificationDocuments())
                .registerEntryId(command.registerId())
                .build();
    }

    @CommandHandler
    public FishingTaxCommandResult handlePayFishingTaxCommand(PayFishingTaxCommand command,
                                                              PayFishingTaxCommandValidator validator,
                                                              PayFishingTaxCommandProcessor processor) throws RulesProcessingException {

        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        validator.validateOrThrow(command, this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final FishingTaxPayedEvent event = (FishingTaxPayedEvent) events.get(0);

        return FishingTaxCommandResult.builder()
                .documents(event.identificationDocuments())
                .registerEntryId(this.registerId)
                .build();
    }

    @CommandHandler
    public void handleOSCreateRegularLicenseCommand(OSCreateRegularLicenseCommand command, OSCreateRegularLicenseCommandValidator validator, OSCreateRegularLicenseCommandProcessor processor) {
        validator.validateOrThrow(command, this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.CREATE_IF_MISSING)
    public void handle(OSCreateVacationLicenseCommand command, OSCreateVacationLicenseCommandValidator validator, OSCreateVacationLicenseCommandProcessor processor) {
        ensureFederalStatesMatchFederalState(command.federalState(), command.fee().getFederalState());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @CommandHandler
    public void handle(OSReplaceCardCommand command, OSReplaceCardCommandValidator validator, OSReplacementCardOrderedCommandProcessor processor) {
        validator.validateOrThrow(command, this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @CommandHandler
    public void handleOSPayTaxCommand(OSPayTaxCommand command, OSTaxCommandValidator validator, OSPayTaxCommandProcessor processor) {
        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @CommandHandler
    public CommandResult handle(ChangePersonalDataCommand command, ChangePersonalDataCommandProcessor processor) {
        ensureUserJurisdictionMatches(command.userDetails());
        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final PersonalDataChangedEvent event = (PersonalDataChangedEvent) events.get(0);

        return ChangePersonCommandResult.builder()
                .documents(event.identificationDocuments())
                .person(event.person())
                .registerEntryId(this.registerId)
                .build();
    }


    @CommandHandler
    public CommandResult handle(OrderReplacementCardCommand command,
                                OrderReplacementCardCommandValidator validator,
                                OrderReplacementCardCommandProcessor processor) throws RulesProcessingException {

        ensureUserJurisdictionMatches(command.userDetails());
        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final ReplacementCardOrderedEvent event = (ReplacementCardOrderedEvent) events.get(0);

        return LicenseCommandResult.builder()
                .fishingLicense(event.fishingLicense())
                .person(event.person())
                .documents(event.identificationDocuments())
                .registerEntryId(this.registerId)
                .build();
    }

    @CommandHandler
    public CommandResult handleCreateRegularFishingLicenseCommand(
            CreateRegularLicenseCommand command,
            CreateRegularLicenseCommandValidator validator,
            CreateRegularLicenseCommandProcessor processor) throws RulesProcessingException {

        // if a person has no jurisdiction (for example an exam taker) then we do not assert the jurisdiction
        if (this.jurisdiction != null) {
            ensureUserJurisdictionMatches(command.userDetails());
        }

        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final RegularLicenseCreatedEvent event = (RegularLicenseCreatedEvent) events.get(0);

        return LicenseCommandResult.builder()
                .fishingLicense(event.fishingLicense())
                .person(event.person())
                .documents(event.identificationDocuments())
                .registerEntryId(this.registerId)
                .build();
    }

    @CommandHandler
    @CreationPolicy(AggregateCreationPolicy.CREATE_IF_MISSING)
    public CommandResult handleCreateVacationLicenseCommand(
            CreateVacationLicenseCommand command,
            CreateVacationLicenseCommandValidator validator,
            CreateVacationLicenseCommandProcessor processor) throws RulesProcessingException {

        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        validator.validateOrThrow(command, this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final VacationLicenseCreatedEvent event = (VacationLicenseCreatedEvent) events.getFirst();

        return LicenseCommandResult.builder()
                .fishingLicense(event.fishingLicense())
                .person(event.person())
                .documents(event.identificationDocuments())
                .registerEntryId(this.registerId)
                .build();
    }

    @CommandHandler
    public CommandResult handleExtendLicenseCommand(
            ExtendLicenseCommand command,
            ExtendLicenseCommandValidator validator,
            ExtendLicenseCommandProcessor processor) throws RulesProcessingException {

        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        validator.validateOrThrow(command, this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final LicenseExtendedEvent event = (LicenseExtendedEvent) events.getFirst();

        return LicenseCommandResult.builder()
                .person(event.person())
                .documents(event.identificationDocuments())
                .registerEntryId(this.registerId)
                .build();
    }

    @CommandHandler
    public CommandResult handle(MoveJurisdictionCommand command, MoveJurisdictionCommandValidator validator, MoveJurisdictionCommandProcessor processor) throws RulesProcessingException {
        ensureCommandTaxesMatchUserJurisdiction(command.taxes(), command.userDetails());

        if (this.jurisdiction != null && AccessChecker.registerEntryInUserJurisdiction(this.jurisdiction, command.userDetails())) {
            throw new JurisdictionAlreadyAssignedException(jurisdiction.getFederalState());
        }

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final JurisdictionMovedEvent event = (JurisdictionMovedEvent) events.get(0);

        return JurisdictionCommandResult.builder()
                .documents(event.identificationDocuments())
                .jurisdiction(event.newJurisdiction())
                .registerEntryId(this.registerId)
                .build();
    }

    @CommandHandler
    public BanCommandResult handle(BanTemporarilyCommand command, BanTemporarilyCommandValidator validator, BanTemporarilyCommandProcessor processor) {
        ensureUserJurisdictionMatches(command.userDetails());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final BannedEvent event = (BannedEvent) events.get(0);

        return BanCommandResult.builder()
                .registerEntryId(this.registerId)
                .ban(event.ban())
                .build();
    }

    @CommandHandler
    public BanCommandResult handle(BanPermanentlyCommand command, BanPermanentlyCommandValidator validator, BanPermanentlyCommandProcessor processor) {
        ensureUserJurisdictionMatches(command.userDetails());

        validator.validateOrThrow(command);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);

        final BannedEvent event = (BannedEvent) events.get(0);

        return BanCommandResult.builder()
                .registerEntryId(this.registerId)
                .ban(event.ban())
                .build();
    }

    @CommandHandler
    public void handle(UnbanCommand command, UnbanCommandProcessor processor) {
        ensureUserJurisdictionMatches(command.userDetails());
        if (this.ban == null) {
            throw new IllegalStateException("No Ban found for register Id: " + this.registerId);
        }

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @CommandHandler
    public void handle(OSExtendFishingLicenseCommand command, OSExtendFishingLicenseCommandValidator validator, OSExtendFishingLicenseCommandProcessor processor) throws RulesProcessingException {
        ensureFederalStatesMatchFederalState(command.federalState(), command.fee().getFederalState());

        validator.validateOrThrow(command, this);

        final List<AxonEvent> events = processor.process(command, this);

        events.forEach(AggregateLifecycle::apply);
    }

    @EventSourcingHandler
    public void on(RegularLicenseDigitizedEvent event) {
        this.registerId = event.registerId();

        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);

        this.taxes.addAll(event.taxes());
        this.fees.addAll(event.fees());
        this.identificationDocuments.addAll(event.identificationDocuments());
        this.jurisdiction = event.jurisdiction();
        this.qualificationsProofs.addAll(event.qualificationsProofs());

        if (event.fishingLicense() != null) {
            this.fishingLicenses.add(event.fishingLicense());
        }
    }

    @EventSourcingHandler
    public void on(QualificationsProofCreatedEvent event) {
        this.registerId = event.registerEntryId();

        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);
        this.qualificationsProofs.add(event.qualificationsProof());

    }

    @EventSourcingHandler
    private void on(PersonalDataChangedEvent event) {
        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);
        this.taxes.addAll(event.taxes());
        this.identificationDocuments.addAll(event.identificationDocuments());
    }

    @EventSourcingHandler
    public void on(PersonCreatedEvent event) {
        this.registerId = event.registerId();
        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);

        this.taxes.addAll(event.payedTaxes());
        this.taxes.addAll(event.taxes());

        // if online service data available set it
        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        this.identificationDocuments.addAll(event.identificationDocuments());
    }

    @EventSourcingHandler
    public void on(FishingTaxPayedEvent event) {
        if (event.person() != null) {
            this.setPersonWithoutOverwritingAddress(event.person());
        }

        // if online service data available set it
        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        // add new taxes and documents for taxes
        this.taxes.addAll(event.taxes());
        this.identificationDocuments.addAll(event.identificationDocuments());

    }

    @EventSourcingHandler
    public void on(RegularLicenseCreatedEvent event) {
        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);

        this.taxes.addAll(event.taxes());
        this.fees.addAll(event.fees());
        this.identificationDocuments.addAll(event.identificationDocuments());
        this.jurisdiction = event.jurisdiction();

        // if online service data available set it
        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        if (event.fishingLicense() != null) {
            this.fishingLicenses.add(event.fishingLicense());
        }
    }

    @EventSourcingHandler
    public void on(VacationLicenseCreatedEvent event) {
        this.registerId = event.registerEntryId();

        if (event.person() != null) {
            this.setPersonWithoutOverwritingAddress(event.person());
        }

        // if online service data available set it
        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        this.taxes.addAll(event.taxes());
        this.fees.addAll(event.fees());
        this.identificationDocuments.addAll(event.identificationDocuments());
        this.fishingLicenses.add(event.fishingLicense());
    }


    @EventSourcingHandler
    public void on(LicenseExtendedEvent event) {
        if (event.person() != null) {
            this.setPersonWithoutOverwritingAddress(event.person());
        }

        // add new taxes and documents for taxes
        this.taxes.addAll(event.taxes());
        this.fees.addAll(event.fees());
        this.identificationDocuments.addAll(event.identificationDocuments());

        // if online service data available set it
        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        this.fishingLicenses.stream()
                .filter(fishingLicense -> fishingLicense.getNumber().equals(event.licenseNumber()))
                .findFirst()
                .ifPresentOrElse(
                        fishingLicense -> fishingLicense.getValidityPeriods().add(event.validityPeriod()),
                        () -> {
                            throw new IllegalStateException("License with number" + event.licenseNumber() + "Not Found");
                        }
                );

    }

    @EventSourcingHandler
    public void on(JurisdictionMovedEvent event) {
        this.registerId = event.registerId();
        this.jurisdiction = event.newJurisdiction();
        this.taxes.addAll(event.taxes());
        this.identificationDocuments.addAll(event.identificationDocuments());
    }


    @EventSourcingHandler
    public void on(BannedEvent event) {
        this.registerId = event.registerId();

        this.ban = event.ban();
    }

    @EventSourcingHandler
    public void on(ReplacementCardOrderedEvent event) {
        this.registerId = event.registerId();
        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);
        this.taxes.addAll(event.taxes());

        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        this.fees.addAll(event.fees());
        this.identificationDocuments.addAll(event.identificationDocuments());

        final FishingLicense reorderedLicense = this.fishingLicenses.stream()
                .filter(license -> license.getNumber().equals(event.fishingLicense().getNumber()))
                .findFirst()
                .orElseThrow(IllegalStateException::new);
        reorderedLicense.setIssuingFederalState(event.federalState());
    }

    @EventSourcingHandler
    public void on(UnbannedEvent event) {
        this.registerId = event.registerEntryId();
        this.ban = null;
    }

    @EventSourcingHandler
    public void on(LimitedLicenseCreatedEvent event) {
        this.registerId = event.registerId();
        this.person = event.person();
        PersonUtils.setBirthnameIfMissing(person);

        this.taxes.addAll(event.taxes());
        this.fees.addAll(event.fees());
        this.identificationDocuments.addAll(event.identificationDocuments());
        this.jurisdiction = event.jurisdiction();

        if(event.inboxReference() != null) {
            this.inboxReference = event.inboxReference();
        }
        if(event.serviceAccountId() != null) {
            this.serviceAccountId = event.serviceAccountId();
        }

        if (event.fishingLicense() != null) {
            this.fishingLicenses.add(event.fishingLicense());
        }
    }

    private FishingLicense findLicenseByLicenseNumber(String licenseNumber) {
        return fishingLicenses.stream()
                .filter(l -> l.getNumber().equals(licenseNumber))
                .findFirst()
                .orElse(null);
    }

    private void ensureUserJurisdictionMatches(UserDetails userDetails) {
        if (!AccessChecker.registerEntryInUserJurisdiction(this.jurisdiction, userDetails)) {
            throw new JurisdictionMismatchException();
        }
    }

    private void ensureCommandTaxesMatchUserJurisdiction(List<Tax> taxes, UserDetails userDetails) {
        if (!AccessChecker.commandTaxesMatchUserJurisdiction(taxes, userDetails)) {
            throw new TaxInWrongJurisdictionException();
        }
    }

    private void ensureFederalStatesMatchFederalState(FederalState federalState, String federalState1) {
        if (!federalState.toString().equals(federalState1)) {
            throw new FederalStateMismatchException();
        }
    }

    private void setPersonWithoutOverwritingAddress(Person person) {
        final Address currentAddress = this.person == null ? person.getAddress() : this.person.getAddress();
        this.person = person;
        this.person.setAddress(currentAddress);

        PersonUtils.setBirthnameIfMissing(person);
    }
}
