package de.adesso.fischereiregister.core.service;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.LicenseFeePriceInformation;
import de.adesso.fischereiregister.core.ports.contracts.TaxPriceInformation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class VacationFeeSeparationServiceTest {
    private VacationFeeSeparationService vacationFeeSeparationService;

    private TenantInformationPort tenantInformationService;

    @BeforeEach
    public void setUp() throws RulesProcessingException {
        tenantInformationService = mock(TenantInformationPort.class);
        vacationFeeSeparationService = new VacationFeeSeparationService(tenantInformationService);

        when(tenantInformationService.getTaxPriceInformation(any(), eq(1), eq(true), any())).thenReturn(new TaxPriceInformation(BigDecimal.valueOf(7.0), 1));
        when(tenantInformationService.getTaxPriceInformation(any(), eq(2), eq(true), any())).thenReturn(new TaxPriceInformation(BigDecimal.valueOf(14.0), 1));
        when(tenantInformationService.getLicenseInformation(any(), any(), any())).thenReturn(new LicenseFeePriceInformation(BigDecimal.valueOf(20.0), LocalDate.MAX, false));
    }

    @Test
    public void testSingleYearFeeExtractionWhenPeriodContained() throws RulesProcessingException {
        // GIVEN
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2021, 4, 1));
        validityPeriod.setValidTo(LocalDate.of(2021, 4, 2));

        final Fee fee = buildFee(27.0);

        // WHEN
        final SeparatedPrice separatedPrice = vacationFeeSeparationService.separateTaxesAndFees(validityPeriod, fee, FederalState.SH);

        // THEN
        final List<Fee> fees = separatedPrice.getFees();
        final List<Tax> taxes = separatedPrice.getTaxes();

        assertEquals(1, fees.size());
        final Fee actualFee = fees.get(0);
        assertEquals(20.0, actualFee.getPaymentInfo().getAmount());

        assertEquals(1, taxes.size());
        final Tax actualTax = taxes.get(0);
        assertEquals(7.0, actualTax.getPaymentInfo().getAmount());

        // Verify tax validity dates are set to first of January and last of December
        assertEquals(LocalDate.of(2021, 1, 1), actualTax.getValidFrom());
        assertEquals(LocalDate.of(2021, 12, 31), actualTax.getValidTo());
    }

    @Test
    public void testSingleYearFeeExtractionWhenPeriodExactly1Year() throws RulesProcessingException {
        // GIVEN
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2021, 1, 1));
        validityPeriod.setValidTo(LocalDate.of(2021, 12, 31));

        final Fee fee = buildFee(27.0);

        // WHEN
        final SeparatedPrice separatedPrice = vacationFeeSeparationService.separateTaxesAndFees(validityPeriod, fee, FederalState.SH);

        // THEN
        final List<Fee> fees = separatedPrice.getFees();
        final List<Tax> taxes = separatedPrice.getTaxes();

        assertEquals(1, fees.size());
        final Fee actualFee = fees.get(0);
        assertEquals(20.0, actualFee.getPaymentInfo().getAmount());

        assertEquals(1, taxes.size());
        final Tax actualTax = taxes.get(0);
        assertEquals(7.0, actualTax.getPaymentInfo().getAmount());

        // Verify tax validity dates are set to first of January and last of December
        assertEquals(LocalDate.of(2021, 1, 1), actualTax.getValidFrom());
        assertEquals(LocalDate.of(2021, 12, 31), actualTax.getValidTo());
    }

    @Test
    public void testMultipleYearFeeExtraction() throws RulesProcessingException {
        // GIVEN
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2021, 12, 24));
        validityPeriod.setValidTo(LocalDate.of(2022, 12, 2));

        final Fee fee = buildFee(34.0);

        // WHEN
        final SeparatedPrice separatedPrice = vacationFeeSeparationService.separateTaxesAndFees(validityPeriod, fee, FederalState.SH);

        // THEN
        final List<Fee> fees = separatedPrice.getFees();
        final List<Tax> taxes = separatedPrice.getTaxes();

        assertEquals(1, fees.size());
        assertEquals(1, taxes.size());

        final Fee actualFee = fees.get(0);
        assertEquals(20.0, actualFee.getPaymentInfo().getAmount());

        final Tax actualTax1 = taxes.get(0);
        assertEquals(14.0, actualTax1.getPaymentInfo().getAmount());

        // Verify tax validity dates are set to first of January and last of December
        assertEquals(LocalDate.of(2021, 1, 1), actualTax1.getValidFrom());
        assertEquals(LocalDate.of(2022, 12, 31), actualTax1.getValidTo());
    }

    private Fee buildFee(Double amount) {
        Fee fee = new Fee();

        fee.setFederalState("SH");

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(amount);
        paymentInfo.setType(PaymentType.ONLINE);

        fee.setPaymentInfo(paymentInfo);

        return fee;
    }
}
