package de.adesso.fischereiregister.core.use_case.online_services;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.OSCreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.UUID;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class OSCreateFishingLicenseTest extends OSTest {

    @Test
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();
        final ConsentInfo givenConsentInfo = DomainTestData.createConsentInfo();

        final Jurisdiction givenJurisdiction = new Jurisdiction();
        givenJurisdiction.setFederalState("HH");

        String issuedBy = "Fischfreunde Övelgönne e.V.";

        final String givenOriginalSalt = "12345";
        final String givenReplacementSalt = "67890";

        assertNotEquals(givenOriginalSalt, givenReplacementSalt);
        final FishingLicense givenFishingLicense = new FishingLicense();
        givenFishingLicense.setType(LicenseType.REGULAR);
        givenFishingLicense.setIssuingFederalState(FederalState.HH);

        String transactionId = UUID.randomUUID().toString();
        String certificateCode = "randomString";
        String serviceAccountId = "serviceAccountId";
        String inboxReference = "inboxReference";

        fixture
                .givenCommands(
                        new CreateFishingCertificateCommand(givenId,
                                DomainTestData.jan_2024,
                                givenPerson,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .when(
                        new OSCreateRegularLicenseCommand(
                                givenId,
                                givenOriginalSalt,
                                certificateCode,
                                givenPerson,
                                givenConsentInfo,
                                emptyList(),
                                emptyList(),
                                serviceAccountId,
                                givenJurisdiction.getFederalState(),
                                transactionId,
                                inboxReference
                        )
                )
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(JurisdictionMovedEvent.class)
                        )),
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(RegularLicenseCreatedEvent.class)))
                ))
                .expectState(registerEntry -> {
                    // Assert register ID
                    assertEquals(givenId, registerEntry.getRegisterId());

                    assertNotNull(registerEntry.getJurisdiction());
                    assertEquals(givenJurisdiction.getFederalState(), registerEntry.getJurisdiction().getFederalState());

                    // Assert qualifications proofs
                    assertEquals(1, registerEntry.getQualificationsProofs().size());
                    assertEquals(issuedBy, registerEntry.getQualificationsProofs().get(0).getIssuedBy());

                    // Assert fishing licenses
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    //assertEquals(givenFishingLicense.getNumber(), registerEntry.getFishingLicenses().get(0).getNumber()); can not be tested becaus it is set in the aggregate (registerEntry)

                    assertEquals(2, registerEntry.getIdentificationDocuments().size());

                    checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
                });

    }
}
