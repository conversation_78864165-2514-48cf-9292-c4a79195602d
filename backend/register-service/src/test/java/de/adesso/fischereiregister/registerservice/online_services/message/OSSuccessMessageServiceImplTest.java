package de.adesso.fischereiregister.registerservice.online_services.message;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.message.service.OSInboxService;
import de.adesso.fischereiregister.message.service.model.OSMessageWithAttachments;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.fishing_license_export.FishingLicenseExportService;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.VACATION_FISHING_LICENSE_CREATED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.VACATION_FISHING_LICENSE_EXTENDED_OS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class OSSuccessMessageServiceImplTest {

    @Mock
    private FishingLicenseExportService exportService;
    @Mock
    private OSInboxService osInboxService;
    @Mock
    private OSMessageTemplateResolutionService OSMessageTemplateResolutionService;

    private OSSuccessMessageServiceImpl osSuccessService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        osSuccessService = new OSSuccessMessageServiceImpl(exportService, osInboxService, OSMessageTemplateResolutionService);
    }

    @Test
    void testHandleRegularLicenseCreationSuccess() throws JsonProcessingException {
        // given
        String inboxReference = "Inbox123";
        String federalState = "SH";
        Person person = new Person();
        UUID registerId = UUID.randomUUID();
        String salt = "randomSalt";
        List<IdentificationDocument> documents = Collections.emptyList();

        when(OSMessageTemplateResolutionService.getSubject(FederalState.SH, REGULAR_FISHING_LICENSE_CREATED_OS))
                .thenReturn("License Created Subject");
        when(OSMessageTemplateResolutionService.getText(FederalState.SH, REGULAR_FISHING_LICENSE_CREATED_OS, person))
                .thenReturn("License Created Body");

        // when
        osSuccessService.handleRegularLicenseCreationSuccess(inboxReference, federalState, person, registerId, salt, documents);

        // then
        ArgumentCaptor<OSMessageWithAttachments> messageCaptor = ArgumentCaptor.forClass(OSMessageWithAttachments.class);
        verify(osInboxService).sendMessageWithAttachments(eq(inboxReference), messageCaptor.capture(), anyList());

        OSMessageWithAttachments capturedMessage = messageCaptor.getValue();
        assertEquals("License Created Subject", capturedMessage.getSubject());
        assertEquals("License Created Body", capturedMessage.getBody());
    }

    @Test
    void testHandleLicenseReplacementSuccess() throws JsonProcessingException {
        // given
        String inboxReference = "Inbox123";
        Person person = new Person();
        UUID registerId = UUID.randomUUID();
        String federalState = "SH";
        String salt = "randomSalt";
        List<IdentificationDocument> documents = Collections.emptyList();

        // when
        when(OSMessageTemplateResolutionService.getSubject(FederalState.SH, REGULAR_FISHING_LICENSE_REPLACED_OS))
                .thenReturn("License Replaced Subject");
        when(OSMessageTemplateResolutionService.getText(FederalState.SH, REGULAR_FISHING_LICENSE_REPLACED_OS, person))
                .thenReturn("License Replaced Body");

        osSuccessService.handleLicenseReplacementSuccess(inboxReference, person, registerId, federalState, salt, documents);

        // then
        ArgumentCaptor<OSMessageWithAttachments> messageCaptor = ArgumentCaptor.forClass(OSMessageWithAttachments.class);
        verify(osInboxService).sendMessageWithAttachments(eq(inboxReference), messageCaptor.capture(), anyList());

        OSMessageWithAttachments capturedMessage = messageCaptor.getValue();
        assertEquals("License Replaced Subject", capturedMessage.getSubject());
        assertEquals("License Replaced Body", capturedMessage.getBody());
    }


    @Test
    void testProcessOsAttachments_GeneratesAttachments() throws JsonProcessingException {
        // given
        Person person = new Person();
        UUID registerId = UUID.randomUUID();
        String salt = "randomSalt";
        IdentificationDocument doc = mock(IdentificationDocument.class);
        when(doc.getTax()).thenReturn(TestDataUtil.createAnalogTax());
        when(doc.getDocumentId()).thenReturn("123");
        RenderedContent renderedContent = new RenderedContent("file.pdf", RenderedContentType.PDF, new byte[]{1, 2, 3});
        when(exportService.exportFishingTaxDocument(registerId, salt, person, doc)).thenReturn(renderedContent);

        List<IdentificationDocument> documents = List.of(doc);
        // when
        osSuccessService.handleTaxPaymentSuccess("Inbox123", person, registerId, salt, documents);

        // then
        verify(exportService).exportFishingTaxDocument(registerId, salt, person, doc);
    }

    @Test
    void testHandleFishingLicenseExtendedSuccess_RemovesTaxDocuments() throws Exception {
        // given
        String inboxReference = "Inbox123";
        Person person = new Person();
        UUID registerEntryId = UUID.randomUUID();
        String salt = "randomSalt";

        IdentificationDocument docWithTax = TestDataUtil.createIdentificationDocumentPDF("tet");
        IdentificationDocument docWithoutTax = TestDataUtil.createIdentificationDocumentPDF("tet");
        docWithTax.setTax(TestDataUtil.createAnalogTax());

        List<IdentificationDocument> documents = List.of(docWithTax, docWithoutTax);

        when(OSMessageTemplateResolutionService.getSubject(FederalState.SH, VACATION_FISHING_LICENSE_EXTENDED_OS))
                .thenReturn("Subject");
        when(OSMessageTemplateResolutionService.getDisplayName(FederalState.SH, VACATION_FISHING_LICENSE_EXTENDED_OS))
                .thenReturn("DisplayName");
        when(OSMessageTemplateResolutionService.getText(FederalState.SH, VACATION_FISHING_LICENSE_EXTENDED_OS, person))
                .thenReturn("Body");

        // when
        osSuccessService.handleFishingLicenseExtendedSuccess(inboxReference, person, registerEntryId, salt, documents);

        // then
        verify(exportService, never()).exportFishingTaxDocument(any(), any(), any(), eq(docWithTax));
    }

    @Test
    void testHandleFishingLicenseVacationSuccess_RemovesTaxDocuments() throws Exception {
        // given
        String inboxReference = "Inbox123";
        Person person = new Person();
        UUID registerEntryId = UUID.randomUUID();
        String salt = "randomSalt";

        IdentificationDocument docWithTax = TestDataUtil.createIdentificationDocumentPDF("tet");
        IdentificationDocument docWithoutTax = TestDataUtil.createIdentificationDocumentPDF("tet");
        docWithTax.setTax(TestDataUtil.createAnalogTax());

        List<IdentificationDocument> documents = List.of(docWithTax, docWithoutTax);

        when(OSMessageTemplateResolutionService.getSubject(FederalState.SH, VACATION_FISHING_LICENSE_CREATED_OS))
                .thenReturn("Subject");
        when(OSMessageTemplateResolutionService.getDisplayName(FederalState.SH, VACATION_FISHING_LICENSE_CREATED_OS))
                .thenReturn("DisplayName");
        when(OSMessageTemplateResolutionService.getText(FederalState.SH, VACATION_FISHING_LICENSE_CREATED_OS, person))
                .thenReturn("Body");

        // when
        osSuccessService.handleFishingLicenseExtendedSuccess(inboxReference, person, registerEntryId, salt, documents);

        // then
        verify(exportService, never()).exportFishingTaxDocument(any(), any(), any(), eq(docWithTax));
    }

}
