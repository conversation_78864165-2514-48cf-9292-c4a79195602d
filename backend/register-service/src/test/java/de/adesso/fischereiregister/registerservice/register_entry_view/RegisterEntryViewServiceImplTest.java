package de.adesso.fischereiregister.registerservice.register_entry_view;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.apapters.inmemory.InMemoryRegisterEntryViewRepository;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RegisterEntryViewServiceImplTest {

    @Mock
    private RegisterEntryViewRepository repository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private RegisterEntryViewServiceImpl service;

    @Captor
    private ArgumentCaptor<RegisterEntryView> viewCaptor;

    private RegisterEntry registerEntry;

    @BeforeEach
    public void setUp() throws JsonProcessingException {

        registerEntry = mock(RegisterEntry.class);
        when(registerEntry.getRegisterId()).thenReturn(UUID.fromString("76f84cc4-b543-4b19-a29e-1a8d10396379"));
    }

    @Test
    @DisplayName("RegisterEntryViewService.createRegisterEntryView with registerId, personDto and jurisdictionDto should create a RegisterEntryView with the proper info")
    public void testCreateRegisterEntryViewWithPersonalDataOnly() {
        // Given
        final UUID registerId = UUID.fromString("76f84cc4-b543-4b19-a29e-1a8d10396379");
        final Person person = TestDataUtil.createTestPerson();
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();

        // When
        service.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerId)
                .person(person)
                .jurisdiction(jurisdiction)
                .build()
        );

        // Then
        verify(repository).save(viewCaptor.capture());

        final RegisterEntryView capturedView = viewCaptor.getValue();
        final Person capturedPerson = capturedView.getData().getPerson();

        assertEquals(registerId, capturedView.getRegisterId());
        assertEquals(person.getFirstname(), capturedPerson.getFirstname());
        assertEquals(person.getLastname(), capturedPerson.getLastname());
        assertEquals(person.getBirthname(), capturedPerson.getBirthname());
        assertEquals(person.getBirthdate(), capturedPerson.getBirthdate());
        assertEquals(person.getBirthplace(), capturedPerson.getBirthplace());
        assertEquals(jurisdiction.getFederalState(), capturedView.getData().getJurisdiction().getFederalState());
    }

    @Test
    @DisplayName("Tests that the assembly of the RegisterEntryView is correct, the data is being set.")
    public void testAssembleRegisterEntrySearchView() {
        // Act
        final RegisterEntryView view = service.assembleRegisterEntryView(registerEntry);

        // Assert
        assertEquals(UUID.fromString("76f84cc4-b543-4b19-a29e-1a8d10396379"), view.getRegisterId());
        assertEquals(view.getData(), registerEntry);
    }

    @Test
    @DisplayName("Test the updateRegisterEntryView service method")
    public void testUpdateRegisterEntryView() {
        // Act
        final RegisterEntry regEntry = DomainTestData.createRegisterEntry();
        final RegisterEntryView view = service.assembleRegisterEntryView(regEntry);

        final Person person = DomainTestData.createPerson();
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();
        final List<Tax> tax = DomainTestData.createAnalogTaxesWithOneTax();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        when(repository.findByRegisterId(DomainTestData.registerId)).thenReturn(Optional.of(view));

        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(regEntry.getRegisterId())
                .person(person)
                .jurisdiction(jurisdiction)
                .taxes(tax)
                .identificationDocuments(identificationDocuments)
                .build());
    }


    @Test
    void testCreateRegisterEntryView() {
        // Given: Setup test data
        final UUID registerId = UUID.randomUUID();

        final Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");
        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("Germany");

        // When: Call the method under test
        service.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerId)
                .person(person)
                .jurisdiction(jurisdiction)
                .build()
        );

    }

    @Test
    void testUpdateEntryView() {
        final UUID registerID = DomainTestData.registerId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(new RegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);
        final Person person = DomainTestData.createPersonWithAddress();

        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .person(person)
                .build());

        final RegisterEntryView actualRegisterEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow(EntityNotFoundException::new);
        assertEquals(person.getFirstname(), actualRegisterEntry.getData().getPerson().getFirstname());
    }

    @Test
    void testUpdateEntryView_Jurisdiction_ConsentInfo_Taxes() {
        final UUID registerID = DomainTestData.registerId;
        final Jurisdiction expectedJurisdiction = DomainTestData.createJurisdiction();
        final List<Tax> expectedTaxes = DomainTestData.createAnalogTaxesWithOneTax();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(new RegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .jurisdiction(expectedJurisdiction)
                .taxes(expectedTaxes)
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow(EntityNotFoundException::new).getData();
        assertEquals(expectedJurisdiction, updatedEntry.getJurisdiction());
        assertEquals(expectedTaxes, updatedEntry.getTaxes());
    }

    @Test
    void testUpdateEntryView_Person_ConsentInfo_Taxes() {
        final UUID registerID = DomainTestData.registerId;
        final Person expectedPerson = DomainTestData.createPerson();
        final List<Tax> expectedTaxes = DomainTestData.createAnalogTaxesWithOneTax();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(new RegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .person(expectedPerson)
                .taxes(expectedTaxes)
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow(EntityNotFoundException::new).getData();
        assertEquals(expectedPerson, updatedEntry.getPerson());
        assertEquals(expectedTaxes, updatedEntry.getTaxes());
    }

    @Test
    void testUpdateEntryView_Person_ConsentInfo_Taxes_WithEmptyTaxes() {
        final UUID registerID = DomainTestData.registerId;
        final Person expectedPerson = DomainTestData.createPerson();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(new RegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .person(expectedPerson)
                .taxes(new ArrayList<>())
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData();
        assertEquals(expectedPerson, updatedEntry.getPerson());
    }

    @Test
    void testUpdateEntryView_Person_ConsentInfo_Taxes_WithNullPerson() {
        final UUID registerID = DomainTestData.registerId;
        final Person personBefore = DomainTestData.createPerson();
        final List<Tax> expectedTaxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerID);
        registerEntry.setPerson(personBefore);
        registerEntryView.setData(registerEntry);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .person(null)
                .taxes(expectedTaxes)
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData();
        assertEquals(personBefore, updatedEntry.getPerson());
    }

    @Test
    void testUpdateEntryView_ConsentInfo_Person_Fees_Taxes_FishingLicense_IdentificationDocuments() {
        final UUID registerID = DomainTestData.registerId;
        final Person expectedPerson = DomainTestData.createPerson();
        final List<Tax> expectedTaxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<Fee> expectedFees = DomainTestData.createAnalogFeesWithOneFee();
        final FishingLicense fishingLicense = TestDataUtil.createFishingLicense();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(new RegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();


        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .jurisdiction(jurisdiction)
                .person(expectedPerson)
                .fees(expectedFees)
                .taxes(expectedTaxes)
                .fishingLicense(fishingLicense)
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData();
        assertEquals(expectedPerson, updatedEntry.getPerson());
        assertEquals(expectedTaxes, updatedEntry.getTaxes());
        assertEquals(jurisdiction, updatedEntry.getJurisdiction());
    }

    @Test
    void testUpdateEntryView_ConsentInfo_Person_Fees_Taxes_FishingLicense_IdentificationDocumentsWithFilledPreData() {
        final UUID registerID = DomainTestData.registerId;
        final Person expectedPerson = DomainTestData.createPerson();
        final List<Tax> expectedTaxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<Fee> expectedFees = DomainTestData.createAnalogFeesWithOneFee();
        final FishingLicense fishingLicense = TestDataUtil.createFishingLicense();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry1 = DomainTestData.createRegisterEntry();
        registerEntryView.setData(registerEntry1);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);
        final int expectedTaxesSize = expectedTaxes.size() + registerEntry1.getTaxes().size();

        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();

        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .jurisdiction(jurisdiction)
                .person(expectedPerson)
                .fees(expectedFees)
                .taxes(expectedTaxes)
                .fishingLicense(fishingLicense)
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData();
        assertEquals(expectedPerson, updatedEntry.getPerson());
        assertEquals(expectedTaxesSize, updatedEntry.getTaxes().size()); // following is not possible because with the in memory repos the updatedEntry == registerEntry1 (expectedTaxes.size() + registerEntry1.getTaxes().size())
    }

    @Test
    void testUpdateEntryView_ConsentInfo_Person_Fees_Taxes_FishingLicense_IdentificationDocumentsWithEmptyParameters() {
        final UUID registerID = DomainTestData.registerId;
        final Person expectedPerson = DomainTestData.createPerson();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry1 = DomainTestData.createRegisterEntry();
        registerEntryView.setData(registerEntry1);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();


        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .jurisdiction(jurisdiction)
                .person(expectedPerson)
                .fees(new ArrayList<>())
                .taxes(new ArrayList<>())
                .fishingLicense(null)
                .identificationDocuments(identificationDocuments)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData();
        assertEquals(expectedPerson, updatedEntry.getPerson());
    }

    @Test
    void testUpdateEntryView_Ban() {
        // set up
        final UUID registerID = DomainTestData.registerId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(DomainTestData.createRegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        // test data
        final Ban expectedBan = TestDataUtil.createBan();

        // Act
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .ban(expectedBan)
                .build());

        // Assert
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData();
        assertEquals(expectedBan, updatedEntry.getBan());
    }


    @Test
    @DisplayName("RegisterEntryViewService.updateRegisterEntryViewWithNewIdentificationDocument should update the view to to replace a identificationDocument ")
    void testReplaceAIdentificationDocumentCardForAFishingLicense() {
        //GIVEN
        final UUID registerID = DomainTestData.registerId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(DomainTestData.createPersonWithAddress());

        final List<FishingLicense> fishingLicensesBeforeTest = registerEntry.getFishingLicenses();

        registerEntryView.setData(registerEntry);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        final Person newPerson = DomainTestData.createPersonWithAddress();
        newPerson.setFirstname("updated");
        newPerson.setLastname("updated");

        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("SH00111122223333");
        fishingLicense.setIssuingFederalState(FederalState.BE);
        fishingLicensesBeforeTest.add(fishingLicense);

        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setFishingLicense(fishingLicense);

        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        identificationDocuments.add(identificationDocument);


        final Fee newFee = new Fee();
        final List<Fee> newFees = List.of(newFee);
        final Tax newTax = new Tax();
        final List<Tax> newTaxes = List.of(newTax);

        final FederalState newIssuingState = FederalState.BE;

        //WHEN
        service.reorderCardAndUpdateRegisterEntry(registerID, newPerson, newFees, newTaxes, identificationDocuments, fishingLicense.getNumber(), newIssuingState);

        //THEN
        inMemoryRegisterEntryViewRepository.findById(registerID).ifPresentOrElse(
                value -> {

                    final RegisterEntry updatedRegisterEntry = value.getData();


                    assertEquals(newPerson.getFirstname(), updatedRegisterEntry.getPerson().getFirstname());
                    assertTrue(updatedRegisterEntry.getTaxes().contains(newTax));
                    assertTrue(updatedRegisterEntry.getFees().contains(newFee));
                    assertTrue(updatedRegisterEntry.getIdentificationDocuments().contains(identificationDocument));

                    // Only the fishing license issuingState should have changed
                    assertEquals(fishingLicensesBeforeTest, updatedRegisterEntry.getFishingLicenses());

                    final FederalState actualNewState = updatedRegisterEntry.getFishingLicenses().get(0).getIssuingFederalState();
                    assertEquals(newIssuingState, actualNewState);
                },

                () -> fail("could not find registerEntry in inMemory Repository")
        );


    }

    @Test
    @DisplayName("RegisterEntryViewService.updateRegisterEntryViewWithNewIdentificationDocument sets the new issuing state of the license.")
    public void testUpdateRegisterEntryViewWithIdentificationDocumentSetsNewIssuingState() {
        //GIVEN
        final UUID registerID = DomainTestData.registerId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(DomainTestData.createPersonWithAddress());

        final List<FishingLicense> fishingLicensesBeforeTest = registerEntry.getFishingLicenses();

        registerEntryView.setData(registerEntry);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        final Person person = registerEntry.getPerson();

        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("SH00111122223333");
        fishingLicense.setIssuingFederalState(FederalState.BE);
        fishingLicensesBeforeTest.add(fishingLicense);

        final FederalState newIssuingState = FederalState.HH;

        //WHEN
        service.reorderCardAndUpdateRegisterEntry(registerID, person, List.of(), List.of(), List.of(), fishingLicense.getNumber(), newIssuingState);

        //THEN
        inMemoryRegisterEntryViewRepository.findById(registerID).ifPresentOrElse(
                value -> {

                    final RegisterEntry updatedRegisterEntry = value.getData();

                    assertEquals(updatedRegisterEntry.getFishingLicenses().size(), fishingLicensesBeforeTest.size());

                    final FishingLicense updatedFishingLicense = updatedRegisterEntry.getFishingLicenses().get(0);
                    assertEquals(newIssuingState, updatedFishingLicense.getIssuingFederalState());

                },

                () -> fail("could not find registerEntry in inMemory Repository")
        );

    }


    @Test
    @DisplayName("RegisterEntryViewServiceImpl.createRegisterEntryView should make at least a call to repository.save")
    void testcreateRegisterEntryViewWithQualificationsProof() {
        //GIVEN


        //WHEN
        service.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(UUID.randomUUID())
                .person(new Person())
                .qualificationsProofs(List.of(new QualificationsProof()))
                .build()
        );

        //THEN
        verify(repository, times(1)).save(any());

    }

    @Test
    @DisplayName("RegisterEntryViewServiceImpl.updateRegisterEntryView should make at least a call to repository.save")
    void testcreateRegisterEntryViewWithBan() {
        //GIVEN

        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setData(new RegisterEntry());

        when(repository.findByRegisterId(any())).thenReturn(Optional.of(registerEntryView));

        //WHEN
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(null)
                .ban(new Ban())
                .build());
        //THEN

        verify(repository, times(1)).save(any());

    }

    @Test
    @DisplayName("RegisterEntryViewServiceImpl.updateRegisterEntryView should make at least a call to repository.save")
    void testCreateRegisterEntryViewWithIdentificationDocuments() {
        //GIVEN
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setData(new RegisterEntry());


        when(repository.findByRegisterId(any())).thenReturn(Optional.of(registerEntryView));

        //WHEN
        service.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(UUID.randomUUID())
                .person(new Person())
                .identificationDocuments(List.of())
                .fishingLicense(new FishingLicense())
                .build()
        );

        // THEN
        verify(repository, times(1)).save(any());

    }

    @Test
    @DisplayName("RegisterEntryViewServiceImpl.createRegisterEntryView should make at least a call to repository.save")
    void testCreateRegisterEntryViewWithTaxesAndConsentInfo() {
        //GIVEN
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setData(new RegisterEntry());
        List<IdentificationDocument> taxIdentificationDocuments = TestDataUtil.createIdentificationDocuments();


        when(repository.findByRegisterId(any())).thenReturn(Optional.of(registerEntryView));

        //WHEN
        service.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(UUID.randomUUID())
                .person(new Person())
                .taxes(List.of())
                .identificationDocuments(taxIdentificationDocuments)
                .build()
        );

        //THEN
        verify(repository, times(1)).save(any());

    }

    @Test
    @DisplayName("RegisterEntryViewServiceImpl.deleteBan should delete the ban from the in memory repository")
    void testDeleteBanFromRegisterEntryView() {
        // given
        final UUID registerID = DomainTestData.registerId;
        final UUID banId = TestDataUtil.banId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry1 = new RegisterEntry();
        registerEntry1.setRegisterId(registerID);
        final Ban ban = new Ban();
        ban.setBanId(banId);
        registerEntry1.setBan(ban);
        registerEntryView.setData(registerEntry1);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);

        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        // when
        service.deleteBan(registerID);

        // then
        assertNull(inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData().getBan());
    }


    @Test
    @DisplayName("RegisterEntryViewServiceImpl.deleteBan is called with for a person which is not banned at all ")
    void testDeleteBanForPersonWhoIsNotBanned() {
        // given
        final UUID registerID = DomainTestData.registerId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerID);
        registerEntryView.setData(registerEntry);
        inMemoryRegisterEntryViewRepository.save(registerEntryView);

        assertNull(inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData().getBan()); // not ban exisists which is ok
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);


        // when
        service.deleteBan(registerID); // should not throw an exception

        // then
        assertNull(inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow().getData().getBan()); // again no ban exists
    }


    @Test
    @DisplayName("RegisterEntryViewServiceImpl.updateRegisterEntryView is called for a person who is banned, but the ban has the wrong id so it is not deleted ")
    void testUpdateRegisterEntryViewOtherCase() {
        final UUID registerID = DomainTestData.registerId;
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerID);
        registerEntryView.setData(new RegisterEntry());
        inMemoryRegisterEntryViewRepository.save(registerEntryView);
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);
        final Person person = DomainTestData.createPersonWithAddress();

        // WHEN
        service.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerID)
                .person(person)
                .taxes(DomainTestData.createAnalogTaxesWithOneTax())
                .fees(DomainTestData.createAnalogFeesWithOneFee())
                .build());

        // THEN
        final RegisterEntryView actualRegisterEntry = inMemoryRegisterEntryViewRepository.findById(registerID).orElseThrow(EntityNotFoundException::new);
        assertEquals(person.getFirstname(), actualRegisterEntry.getData().getPerson().getFirstname());

    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void testCreateOrUpdateRegisterEntryView(boolean createNew) {
        // Given: Setup test data
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        final UUID registerId = UUID.randomUUID();

        final Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");

        if (!createNew) {
            RegisterEntryView registerEntryView = new RegisterEntryView();
            registerEntryView.setRegisterId(registerId);
            registerEntryView.setData(new RegisterEntry());
            registerEntryView.getData().setPerson(person);

            inMemoryRegisterEntryViewRepository.save(registerEntryView);
        }

        final List<Fee> fees = DomainTestData.createAnalogFeesWithOneFee();
        final List<Tax> taxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final FishingLicense license = DomainTestData.createLicense();


        // When: Call the method under test
        service.createOrUpdateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(registerId)
                .person(person)
                .fishingLicense(license)
                .fees(fees)
                .taxes(taxes)
                .identificationDocuments(identificationDocuments)
                .build());

        // Then: Verify that the view was created
        final RegisterEntryView view = inMemoryRegisterEntryViewRepository.findById(registerId).orElse(null);
        assertNotNull(view);

        assertEquals(registerId, view.getRegisterId());

        final Person actualPerson = view.getData().getPerson();
        assertEquals(person, actualPerson);

        final List<Fee> actualFees = view.getData().getFees();
        assertEquals(1, actualFees.size());
        assertEquals(fees.get(0), actualFees.get(0));

        final List<Tax> actualTaxes = view.getData().getTaxes();
        assertEquals(1, actualTaxes.size());
        assertEquals(taxes.get(0), actualTaxes.get(0));

        final List<IdentificationDocument> actualIdentificationDocuments = view.getData().getIdentificationDocuments();
        assertEquals(1, actualIdentificationDocuments.size());
        assertEquals(identificationDocuments.get(0), actualIdentificationDocuments.get(0));
    }

    @Test
    @DisplayName("RegisterEntryViewServiceImpl.extendLicense should update correct validity period")
    void testExtendLicenseSuccessful() {
        // Given: Setup test data

        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        final UUID registerId = UUID.randomUUID();

        final Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");

        final FishingLicense expectedLicense = DomainTestData.createLicense();
        expectedLicense.setType(LicenseType.VACATION);

        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerId);
        registerEntryView.setData(new RegisterEntry());
        registerEntryView.getData().setPerson(person);
        registerEntryView.getData().getFishingLicenses().add(expectedLicense);

        inMemoryRegisterEntryViewRepository.save(registerEntryView);

        ValidityPeriod expectedPeriod = new ValidityPeriod();
        expectedPeriod.setValidFrom(LocalDate.of(2021, 4, 1));
        expectedPeriod.setValidTo(LocalDate.of(2021, 4, 2));

        // When
        service.extendFishingLicense(registerId, expectedLicense.getNumber(), expectedPeriod);

        // Then
        final RegisterEntry updatedEntry = inMemoryRegisterEntryViewRepository.findById(registerId).orElseThrow().getData();
        assertEquals(1, updatedEntry.getFishingLicenses().size());

        final FishingLicense actualLicense = updatedEntry.getFishingLicenses().get(0);
        assertEquals(2, actualLicense.getValidityPeriods().size());
        final ValidityPeriod actualPeriod = actualLicense.getValidityPeriods().get(1);
        assertEquals(expectedPeriod, actualPeriod);
    }

    @Test
    @DisplayName("RegisterEntryViewServiceImpl.extendLicense throws LicenseNotFoundException if trying to extend a non existent license ")
    void testExtendLicenseFailure() {
        // Given: Setup test data
        final InMemoryRegisterEntryViewRepository inMemoryRegisterEntryViewRepository = new InMemoryRegisterEntryViewRepository();
        final RegisterEntryViewService service = new RegisterEntryViewServiceImpl(inMemoryRegisterEntryViewRepository, jdbcTemplate);

        final UUID registerId = UUID.randomUUID();

        final FishingLicense expectedLicense = DomainTestData.createLicense();
        expectedLicense.setType(LicenseType.VACATION);

        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setRegisterId(registerId);
        registerEntryView.setData(new RegisterEntry());
        registerEntryView.getData().getFishingLicenses().add(expectedLicense);

        inMemoryRegisterEntryViewRepository.save(registerEntryView);

        ValidityPeriod expectedPeriod = new ValidityPeriod();
        expectedPeriod.setValidFrom(LocalDate.of(2021, 4, 1));
        expectedPeriod.setValidTo(LocalDate.of(2021, 4, 2));

        // When
        final LicenseNotFoundException exception = assertThrows(LicenseNotFoundException.class, () -> service.extendFishingLicense(registerId, "SH00000000000000", expectedPeriod));

        // Then
        assertEquals("Tried extending the license with number SH00000000000000, but was not found", exception.getMessage());
    }

}
