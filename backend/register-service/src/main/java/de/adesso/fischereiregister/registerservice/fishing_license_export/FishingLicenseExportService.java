package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;

import java.util.UUID;


public interface FishingLicenseExportService {

    RenderedContent exportFishingLicense(UUID registerId, String documentId);

    RenderedContent exportFishingLicense(UUID registerId, String salt, Person person, IdentificationDocument document);

    RenderedContent exportFishingTaxDocument(UUID registerId, String documentId);

    RenderedContent exportFishingCertificate(String fishingCertificateId) throws RulesProcessingException;

    RenderedContent exportFishingTaxDocument(UUID registerEntryId, String salt, Person person, IdentificationDocument document);
}
