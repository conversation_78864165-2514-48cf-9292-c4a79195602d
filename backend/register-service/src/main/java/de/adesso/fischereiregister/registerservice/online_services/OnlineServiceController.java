package de.adesso.fischereiregister.registerservice.online_services;

import de.adesso.fischereiregister.core.commands.OSCreatePersonCommand;
import de.adesso.fischereiregister.core.commands.OSCreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSCreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSExtendFishingLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.exceptions.OnlineServiceProcessingException;
import de.adesso.fischereiregister.core.exceptions.PersonNotChangeableException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.domain.mapper.ConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FederalStateMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FeeMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.ValidityPeriodMapper;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import de.adesso.fischereiregister.registerservice.online_services.determination.OSRegisterDeterminationService;
import de.adesso.fischereiregister.registerservice.online_services.message.OSFailureMessageService;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRegisterDeterminationResult;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import de.adesso.fischereiregister.utils.HashUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.CreateVacationLicenseRequestOS;
import org.openapitools.model.ExtendVacationLicenseRequestOS;
import org.openapitools.model.OrderFishingLicenseRequestOS;
import org.openapitools.model.PayTaxRequestOS;
import org.openapitools.model.ReplaceFishingLicenseRequestOS;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

/*
    Wichtig: Aus dem Keycloak-Nutzer können keine Informationen gelesen werden!!

    Das heißt:
    UserDetailsService darf hier nicht genutzt werden weil
    die daten nicht aus dem eingeloggten Nutzer gelesen werden dürfen
    Alle daten, auch das Bundesland müssen übermittelt werden und werden aus dem Request ausgelesen
    aus federalState: FederalStateAbbreviation.
 */
@RestController
@RequestMapping()
@Slf4j
@AllArgsConstructor
public class OnlineServiceController implements api.OnlineServiceApi {

    private final CommandGateway commandGateway;
    private final OSFailureMessageService osFailureMessageService;
    private final OSRegisterDeterminationService osRegisterDeterminationService;

    @SneakyThrows
    @Override
    public ResponseEntity<?> onlineServiceControllerCreate(@Valid OrderFishingLicenseRequestOS requestOS) {
        ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(requestOS.getConsentInfo());
        Person person = PersonMapper.INSTANCE.toPersonFromOSWithAddressOS(requestOS.getPerson());
        List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(requestOS.getTaxes());
        List<Fee> fees = FeeMapper.INSTANCE.toFees(requestOS.getFees());
        FederalState federalState = FederalStateMapper.INSTANCE.toFederalState(requestOS.getFederalState());
        String serviceAccountId = requestOS.getServiceAccountId();
        String inboxReference = requestOS.getInboxReference();
        String transactionId = requestOS.getTransactionId();
        String certificateNumber = requestOS.getFishingCertificateCode();
        OSMessageTemplate failureOSMessageTemplate = OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS_FAILURE;

        final OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(certificateNumber, person);

        if (!result.isSuccessful()) {
            handleDeterminationResultError(result.getOsStatus(), inboxReference, null, person, federalState, failureOSMessageTemplate);
        }

        final UUID registerEntryId = result.getRegisterEntryId();

        final OSCreateRegularLicenseCommand command = new OSCreateRegularLicenseCommand(
                registerEntryId,
                HashUtils.gensalt(),
                certificateNumber,
                person,
                consentInfo,
                taxes,
                fees,
                serviceAccountId,
                federalState.toString(),
                transactionId,
                inboxReference
        );

        return sendCommand(command, inboxReference, certificateNumber, person, federalState, failureOSMessageTemplate);
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> onlineServiceControllerReplace(@Valid ReplaceFishingLicenseRequestOS requestOS) {
        ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(requestOS.getConsentInfo());
        Person person = PersonMapper.INSTANCE.toPersonFromOSWithAddressOS(requestOS.getPerson());
        List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(requestOS.getTaxes());
        List<Fee> fees = FeeMapper.INSTANCE.toFees(requestOS.getFees());
        FederalState federalState = FederalStateMapper.INSTANCE.toFederalState(requestOS.getFederalState());
        String serviceAccountId = requestOS.getServiceAccountId();
        String inboxReference = requestOS.getInboxReference();
        String licenseNumber = requestOS.getLicenseNumber();
        String transactionId = requestOS.getTransactionId();
        OSMessageTemplate failureOSMessageTemplate = OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS_FAILURE;

        final OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(licenseNumber, person);

        if (!result.isSuccessful()) {
            handleDeterminationResultError(result.getOsStatus(), inboxReference, licenseNumber, person, federalState, failureOSMessageTemplate);
        }

        final UUID registerEntryId = result.getRegisterEntryId();

        OSReplaceCardCommand command = new OSReplaceCardCommand(
                registerEntryId,
                HashUtils.gensalt(),
                licenseNumber,
                person,
                consentInfo,
                taxes,
                fees,
                serviceAccountId,
                federalState.toString(),
                transactionId,
                inboxReference
        );

        return sendCommand(command, inboxReference, licenseNumber, person, federalState, failureOSMessageTemplate);
    }

    private ResponseEntity<?>  sendCommand(Record command, String inboxReference, String licenseNumber, Person person, FederalState federalState, OSMessageTemplate failureOSMessageTemplate) throws Throwable {

        try {
            commandGateway.send(command).get();
            log.info("Command success: {}", command.getClass().getSimpleName());
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (InterruptedException e) {
            log.error("Error processing command: {}", command.getClass().getSimpleName(), e);
            Thread.currentThread().interrupt();
            osFailureMessageService.handleFailure(OSRequestStatus.UNEXPECTED_ERROR, inboxReference, licenseNumber, person, federalState, failureOSMessageTemplate);
            throw e.getCause();
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("Error processing {}", command.getClass().getName(), e);

            if (cause != null) {
                OSRequestStatus osStatus = switch (cause) {
                    case null -> throw e;
                    case PersonNotChangeableException ignored -> OSRequestStatus.PERSON_NOT_CHANGEABLE;
                    default -> OSRequestStatus.UNEXPECTED_ERROR;
                };
                osFailureMessageService.handleFailure(osStatus, inboxReference, licenseNumber, person, federalState, failureOSMessageTemplate);

                throw cause;  // Unwrapped cause
            } else {
                throw e;  // Original exception
            }
        }
    }

    private void handleDeterminationResultError(OSRequestStatus osRequestStatus, String inboxReference, String licenseNumber, Person person, FederalState federalState, OSMessageTemplate failureOSMessageTemplate) {
        osFailureMessageService.handleFailure(osRequestStatus, inboxReference, licenseNumber, person, federalState, failureOSMessageTemplate);
        throw new OnlineServiceProcessingException(osRequestStatus.toString());
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> onlineServiceControllerTaxPayed(@Valid PayTaxRequestOS requestOS) {
        TaxConsentInfo taxConsentInfo = TaxConsentInfoMapper.INSTANCE.toTaxConsentInfo(requestOS.getConsentInfo());
        Person person = PersonMapper.INSTANCE.toPerson(requestOS.getPerson());
        List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(requestOS.getTaxes());
        FederalState federalState = FederalStateMapper.INSTANCE.toFederalState(requestOS.getFederalState());
        String serviceAccountId = requestOS.getServiceAccountId();
        String inboxReference = requestOS.getInboxReference();
        String licenseNumber = requestOS.getLicenseNumber();
        String transactionId = requestOS.getTransactionId();
        OSMessageTemplate failureOSMessageTemplate = OSMessageTemplate.FISHING_TAX_CREATED_OS_FAILURE;

        final OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(licenseNumber, person);
        if (!result.isSuccessful()) {
            return processRegisterEntryNotFoundForPayTax(result, licenseNumber, person, taxes, taxConsentInfo, inboxReference, federalState, serviceAccountId, transactionId, failureOSMessageTemplate);
        }

        final UUID registerEntryId = result.getRegisterEntryId();
        final OSPayTaxCommand command = new OSPayTaxCommand(registerEntryId,
                person,
                taxConsentInfo,
                HashUtils.gensalt(),
                taxes,
                serviceAccountId,
                transactionId,
                federalState.toString(),
                inboxReference);

        return sendCommand(command, inboxReference, licenseNumber, person, federalState, failureOSMessageTemplate);
    }


    @SneakyThrows
    @Override
    public ResponseEntity<?> onlineServiceControllerVacationLicense(CreateVacationLicenseRequestOS requestOS) {
        ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(requestOS.getConsentInfo());
        Person person = PersonMapper.INSTANCE.toPerson(requestOS.getPerson());
        Fee fee = FeeMapper.INSTANCE.toFee(requestOS.getFee());
        ValidityPeriod validityPeriod = ValidityPeriodMapper.INSTANCE.toValidityPeriod(requestOS.getValidityPeriod());

        FederalState federalState = FederalStateMapper.INSTANCE.toFederalState(requestOS.getFederalState());
        String serviceAccountId = requestOS.getServiceAccountId();
        String inboxReference = requestOS.getInboxReference();
        String transactionId = requestOS.getTransactionId();
        OSMessageTemplate failureOSMessageTemplate = OSMessageTemplate.VACATION_FISHING_LICENSE_CREATED_OS_FAILURE;

        OSRegisterDeterminationResult result = osRegisterDeterminationService.determineOrCreateRegisterEntryId(person);

        if (!result.isSuccessful()) {
            handleDeterminationResultError(result.getOsStatus(), inboxReference, null, person, federalState, failureOSMessageTemplate);
        }

        final UUID registerEntryId = result.getRegisterEntryId();

        final OSCreateVacationLicenseCommand command = new OSCreateVacationLicenseCommand(
                registerEntryId,
                person,
                consentInfo,
                fee,
                HashUtils.gensalt(),
                federalState,
                validityPeriod,
                transactionId,
                serviceAccountId,
                inboxReference
        );

        return sendCommand(command, inboxReference, null, person, federalState, failureOSMessageTemplate);
    }

    private ResponseEntity<?> processRegisterEntryNotFoundForPayTax(
            OSRegisterDeterminationResult result,
            String licenseNumber,
            Person person,
            List<Tax> taxes,
            TaxConsentInfo taxConsentInfo,
            String inboxReference,
            FederalState federalState,
            String serviceAccountId,
            String transactionId,
            OSMessageTemplate failureOSMessageTemplate) throws Throwable {
        // if the person is not found in the system but no licenseNumber was given in the request, then a new Register Entry is to be created
        if (result.getOsStatus().equals(OSRequestStatus.PERSON_NOT_FOUND) && licenseNumber == null) {

            final OSCreatePersonCommand createPersonCommand = new OSCreatePersonCommand(
                    UUID.randomUUID(),
                    person,
                    taxes,
                    HashUtils.gensalt(),
                    taxConsentInfo,
                    federalState.toString(),
                    inboxReference,
                    serviceAccountId,
                    transactionId);

            return sendCommand(createPersonCommand, inboxReference, null, person, federalState, failureOSMessageTemplate);
        } else {
            // This is for OSStatusType.MULTIPLE_PERSONS_FOUND
            osFailureMessageService.handleFailure(
                    result.getOsStatus(), inboxReference, licenseNumber, person, federalState,
                    failureOSMessageTemplate);
            return new ResponseEntity<>(result.getOsStatus(), HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    @SneakyThrows
    public ResponseEntity<?> onlineServiceControllerExtendVacationLicense(@Valid ExtendVacationLicenseRequestOS requestOS) {
        ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(requestOS.getConsentInfo());
        Fee fee = FeeMapper.INSTANCE.toFee(requestOS.getFee());
        String serviceAccountId = requestOS.getServiceAccountId();
        String inboxReference = requestOS.getInboxReference();
        String licenseNumber = requestOS.getLicenseNumber();
        ValidityPeriod validityPeriod = ValidityPeriodMapper.INSTANCE.toValidityPeriod(requestOS.getValidityPeriod());
        String transactionId = requestOS.getTransactionId();
        FederalState federalState = FederalStateMapper.INSTANCE.toFederalState(requestOS.getFederalState());
        OSMessageTemplate failureOSMessageTemplate = OSMessageTemplate.VACATION_FISHING_LICENSE_EXTENDED_OS_FAILURE;

        final OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(licenseNumber);
        if (!result.isSuccessful()) {
            handleDeterminationResultError(result.getOsStatus(), inboxReference, licenseNumber, null, federalState, failureOSMessageTemplate);
        }

        final UUID registerEntryId = result.getRegisterEntryId();

        OSExtendFishingLicenseCommand command = new OSExtendFishingLicenseCommand(
                registerEntryId,
                HashUtils.gensalt(),
                requestOS.getLicenseNumber(),
                consentInfo,
                fee,
                federalState,
                validityPeriod,
                serviceAccountId,
                transactionId,
                inboxReference);

        return sendCommand(command, inboxReference, licenseNumber, null, federalState, failureOSMessageTemplate);
    }
}
