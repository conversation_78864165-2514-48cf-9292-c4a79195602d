package de.adesso.fischereiregister.registerservice.admin;


import de.adesso.fischereiregister.core.commands.UnbanCommand;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.view.ban_expiration.services.BanExpirationCollectionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service
@AllArgsConstructor
public class UnbanningScheduledTasks {

    private static final String NIGHTLY = "0 0 0 * * *";

    private final BanExpirationCollectionService banExpirationCollectionService;
    private final CommandGateway commandGateway;


    @Scheduled(cron = NIGHTLY)
    public void removeExpiredBans() {
        log.info("Starting removal of expired bans...");

        banExpirationCollectionService.findRegisterEntryIdsWithExpiredBans(LocalDate.now()).forEach(this::unban);

        log.info("... removal of expired bans completed");
    }


    private void unban(UUID registerId) {
        final UnbanCommand unbanCommand = new UnbanCommand(registerId, UserDetails.SYSTEM_USER);
        try {
            commandGateway.send(unbanCommand).get();
        } catch (InterruptedException e) {
            log.error("Failed to unban {}", registerId, e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("Failed to unban {}", registerId, e);
        }

    }
}
