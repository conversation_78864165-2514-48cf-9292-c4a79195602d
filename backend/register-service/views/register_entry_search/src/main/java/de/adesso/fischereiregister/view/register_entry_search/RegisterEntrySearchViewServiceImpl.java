package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.utils.DateUtils;
import de.adesso.fischereiregister.utils.StringNormalizer;
import de.adesso.fischereiregister.view.register_entry_search.mapper.RegisterEntrySearchViewMapper;
import de.adesso.fischereiregister.view.register_entry_search.mapper.SearchItemMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import org.openapitools.model.SearchItem;
import org.openapitools.model.SearchItemFishingLicensesInner;
import org.openapitools.model.SearchItemPerson;
import org.openapitools.model.SearchItemQualificationsProofsInner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
@AllArgsConstructor
class RegisterEntrySearchViewServiceImpl implements RegisterEntrySearchViewService {

    private final RegisterEntrySearchViewRepository repository;
    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void truncateView() {
        jdbcTemplate.execute("TRUNCATE TABLE register_entry_view");
    }

    @Override
    @Transactional
    public List<SearchItem> search(String query) {

        List<String> fields = Arrays.stream(query.split(",")).map(String::trim).toList();

        Optional<String> identificationNumber = fields.stream()
                .map(this::removeFormat)
                .filter(this::isIdentificationNumber)
                .findFirst();

        if (identificationNumber.isPresent()) {
            // if an identification number is found, the search must only be performed by identification number
            // All the other fields are ignored in this case
            return findByIdentificationNumber(identificationNumber.get());
        }

        String birthdate = null;
        String name = null;
        String birthplace = null;

        for (String field : fields) {
            if (DateUtils.isDate(field)) {
                birthdate = field;
            } else if (name == null) {
                name = StringNormalizer.normalize(field);
            } else {
                birthplace = StringNormalizer.normalize(field);
            }
        }

        return findByPersonDetails(name, birthplace, birthdate);

    }

    @Override
    public Optional<UUID> findRegisterEntryIdByIdentificationNumber(String identificationNumber) {
        return repository.findRegisterEntryIdByIdentificationNumber(identificationNumber);
    }

    @Override
    public List<UUID> findRegisterEntryIdsByPersonDetails(Person person) {
        if (person == null) {
            return Collections.emptyList();
        }

        String title = person.getTitle() != null ? person.getTitle().trim() : null;
        String firstname = person.getFirstname() != null ? person.getFirstname().trim() : null;
        String lastname = person.getLastname() != null ? person.getLastname().trim() : null;
        // the birthname is not a required field, in that case if left empty we match assuming that the request lastname must match with the birthname in the register
        String birthname = person.getBirthname() != null ? person.getBirthname().trim() : lastname;
        String birthplace = person.getBirthplace() != null ? person.getBirthplace().trim() : null;
        String nationality = person.getNationality() != null ? person.getNationality().trim() : null;
        String birthdate = person.getBirthdate() != null ? person.getBirthdate().toString() : null;

        return repository.findRegisterEntryIdsByPersonDetails(
                title,
                firstname,
                lastname,
                birthname,
                birthplace,
                nationality,
                birthdate
        );
    }

    private String removeFormat(String field) {
        return field.replaceAll("[^a-zA-Z0-9]", "");
    }

    private boolean isIdentificationNumber(String field) {
        return field.matches("[A-Za-z]{2}\\d{14}"); // an Identification Number is composed of two letters followed by 14 numbers
    }

    private List<SearchItem> findByIdentificationNumber(String identificationNumber) {
        return repository.findByIdentificationNumber(identificationNumber).map(List::of).orElse(Collections.emptyList());
    }

    private List<SearchItem> findByPersonDetails(String name, String birthplace, String birthdate) {
        if (isNotBlank(name) || isNotBlank(birthplace) || birthdate != null) {
            return repository.findByNormalizedPersonDetails(name, birthplace, birthdate);
        }
        return Collections.emptyList();
    }

    @Override
    public void createRegisterEntrySearchView(UUID registerId, Person person) {


        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerId, person, null, null);

        repository.save(registerEntrySearchView);
    }


    @Override
    public void createRegisterEntrySearchView(UUID registerId, Person person, FishingLicense fishingLicense) {

        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerId, person, fishingLicense, null);

        repository.save(registerEntrySearchView);
    }

    @Override
    public void createRegisterEntrySearchView(UUID registerId, Person person, String fishingCertificateId) {

        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerId, person, null, fishingCertificateId);

        repository.save(registerEntrySearchView);
    }

    private RegisterEntrySearchView getRegisterEntrySearchView(UUID registerId, Person person, FishingLicense fishingLicense, String fishingCertificateId) {

        final String firstname = StringNormalizer.normalize(person.getFirstname());
        final String lastname = StringNormalizer.normalize(person.getLastname());
        final String birthplace = StringNormalizer.normalize(person.getBirthplace());

        RegisterEntrySearchView registerEntrySearchView = RegisterEntrySearchViewMapper.INSTANCE.toView(person, Collections.emptyList());

        registerEntrySearchView.setRegisterId(registerId);

        SearchItem searchItem = new SearchItem();
        searchItem.setRegisterId(registerId.toString());
        searchItem.setPerson(SearchItemMapper.INSTANCE.toSearchItemPerson(person));
        setBirthnameIfMissing(searchItem.getPerson());
        searchItem.setQualificationsProofs(Collections.emptyList());
        searchItem.setFishingLicenses(Collections.emptyList());

        if (fishingCertificateId != null) {
            registerEntrySearchView.addIdentificationNumber(fishingCertificateId);
            searchItem.setQualificationsProofs(List.of(new SearchItemQualificationsProofsInner(fishingCertificateId)));
        }

        if (fishingLicense != null) {

            SearchItemFishingLicensesInner searchRegisterEntryFishingLicense = new SearchItemFishingLicensesInner();
            searchRegisterEntryFishingLicense.setNumber(fishingLicense.getNumber());

            searchItem.setFishingLicenses(List.of(searchRegisterEntryFishingLicense));

            registerEntrySearchView.addIdentificationNumber(fishingLicense.getNumber()); // if there is a fishing license, the fishing license number hat higher precedence than the certificate
        }

        registerEntrySearchView.setNormalizedName(firstname + " " + lastname);
        registerEntrySearchView.setNormalizedBirthplace(birthplace);
        registerEntrySearchView.setData(searchItem);

        registerEntrySearchView.setTitle(person.getTitle());
        registerEntrySearchView.setFirstname(person.getFirstname());
        registerEntrySearchView.setLastname(person.getLastname());
        registerEntrySearchView.setBirthname(person.getBirthname());
        registerEntrySearchView.setBirthplace(person.getBirthplace());
        registerEntrySearchView.setNationality(person.getNationality());

        return registerEntrySearchView;
    }

    private RegisterEntrySearchView findByRegisterId(UUID registerId) {
        return repository.findByRegisterId(registerId).orElseThrow(EntityNotFoundException::new);
    }

    private void updateViewWithPersonData(RegisterEntrySearchView viewToUpdate, Person person) {

        final String firstname = StringNormalizer.normalize(person.getFirstname());
        final String lastname = StringNormalizer.normalize(person.getLastname());
        final String birthplace = StringNormalizer.normalize(person.getBirthplace());

        viewToUpdate.setNormalizedName(firstname + " " + lastname);
        viewToUpdate.setNormalizedBirthplace(birthplace);

        viewToUpdate.setTitle(person.getTitle());
        viewToUpdate.setFirstname(person.getFirstname());
        viewToUpdate.setLastname(person.getLastname());
        viewToUpdate.setBirthname(person.getBirthname());
        viewToUpdate.setBirthplace(person.getBirthplace());
        viewToUpdate.setBirthdate(person.getBirthdate().toString());
        viewToUpdate.setNationality(person.getNationality());
    }

    @Override
    public void updateRegisterEntrySearchView(UUID registerId, Person person) {
        if (person != null) { // added null check
            RegisterEntrySearchView viewToUpdate = findByRegisterId(registerId);

            viewToUpdate.getData().setPerson(SearchItemMapper.INSTANCE.toSearchItemPerson(person));
            setBirthnameIfMissing(viewToUpdate.getData().getPerson());

            updateViewWithPersonData(viewToUpdate, person);

            repository.save(viewToUpdate);
        }
    }

    public void updateRegisterEntrySearchView(UUID registerEntryId, Person person, FishingLicense fishingLicense) {
        SearchItemFishingLicensesInner searchItemFishingLicensesInnerFishingLicense = new SearchItemFishingLicensesInner();
        searchItemFishingLicensesInnerFishingLicense.setNumber(fishingLicense.getNumber());

        RegisterEntrySearchView viewToUpdate = findByRegisterId(registerEntryId);

        updateViewWithPersonData(viewToUpdate, person);

        viewToUpdate.addIdentificationNumber(fishingLicense.getNumber());
        viewToUpdate.getData().setPerson(SearchItemMapper.INSTANCE.toSearchItemPerson(person));
        setBirthnameIfMissing(viewToUpdate.getData().getPerson());

        viewToUpdate.getData().getFishingLicenses().add(searchItemFishingLicensesInnerFishingLicense);

        repository.save(viewToUpdate);
    }

    public void createOrUpdateRegisterEntrySearchView(UUID registerEntryId, Person person, FishingLicense license) {
        RegisterEntrySearchView viewToUpdate = repository.findByRegisterId(registerEntryId).orElse(null);

        if (viewToUpdate == null) {
            viewToUpdate = getRegisterEntrySearchView(registerEntryId, person, license, null);
        } else {
            updateViewWithPersonData(viewToUpdate, person);

            viewToUpdate.addIdentificationNumber(license.getNumber());
            viewToUpdate.getData().setPerson(SearchItemMapper.INSTANCE.toSearchItemPerson(person));

            SearchItemFishingLicensesInner searchItemFishingLicensesInnerFishingLicense = new SearchItemFishingLicensesInner();
            searchItemFishingLicensesInnerFishingLicense.setNumber(license.getNumber());

            viewToUpdate.getData().getFishingLicenses().add(searchItemFishingLicensesInnerFishingLicense);
        }

        repository.save(viewToUpdate);
    }

    private void setBirthnameIfMissing(SearchItemPerson person) {
        if (person.getBirthname() == null || person.getBirthname().isEmpty()) {
            person.setBirthname(person.getLastname());
        }
    }
}
