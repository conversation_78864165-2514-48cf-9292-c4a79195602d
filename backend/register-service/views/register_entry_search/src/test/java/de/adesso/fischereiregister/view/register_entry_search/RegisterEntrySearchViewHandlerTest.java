package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class RegisterEntrySearchViewHandlerTest {


    @Mock
    RegisterEntrySearchViewService service;

    @InjectMocks
    private RegisterEntrySearchViewHandler eventHandler;

    @Test
    public void testOnPayFishingTaxEvent() {

        // Act: Call the event handler method
        final UUID registerId = DomainTestData.registerId;
        final Person person = DomainTestData.createPerson();
        final TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();
        final Tax tax = DomainTestData.createAnalogTax();
        final List<Tax> taxes = new ArrayList<>();
        taxes.add(tax);

        final FishingTaxPayedEvent mockEvent = new FishingTaxPayedEvent(
                registerId,
                consentInfo,
                person,
                taxes,
                null,
                List.of(),
                null,
                null,
                null,
                null,
                SubmissionType.ANALOG);

        eventHandler.on(mockEvent);

        // Assert: Verify that the service method was called with the correct parameter
        verify(service, times(1)).updateRegisterEntrySearchView(registerId, person);
    }


    @Test
    public void testOnQualificationsProofCreatedEvent() {
        //GIVEN
        final String fishingCertificateId = UUID.randomUUID().toString();
        final QualificationsProof proof = new QualificationsProof();
        proof.setFishingCertificateId(fishingCertificateId);
        final Person person = DomainTestData.createPerson();
        final UUID registerEntryId = UUID.randomUUID();
        final QualificationsProofCreatedEvent event = new QualificationsProofCreatedEvent(registerEntryId,
                proof,
                person
        );

        //WHEN
        eventHandler.on(event);
        //THEN
        verify(service, times(1)).createRegisterEntrySearchView(registerEntryId, person, fishingCertificateId);
    }
}
