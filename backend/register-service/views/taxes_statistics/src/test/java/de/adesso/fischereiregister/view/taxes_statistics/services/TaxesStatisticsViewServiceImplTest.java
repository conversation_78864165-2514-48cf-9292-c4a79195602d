package de.adesso.fischereiregister.view.taxes_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.InMemoryTaxesStatisticsViewRepository;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class TaxesStatisticsViewServiceImplTest {

    private InMemoryTaxesStatisticsViewRepository repository;
    private TaxesStatisticsViewService taxesStatisticsViewService;

    @BeforeEach
    void setUp() {
        repository = new InMemoryTaxesStatisticsViewRepository();
        taxesStatisticsViewService = new TaxesStatisticsViewServiceImpl(repository);
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should create a new entry if none exists")
    void testUpdateOrCreateStatistic_NewEntry() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        int duration = 1;
        int year = 2024;
        double revenue = 50.0;

        // When
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, duration, year, revenue);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, source, duration, year);

        assertTrue(result.isPresent());
        assertEquals(1, result.get().getCount());
        assertEquals(revenue, result.get().getRevenue());
        assertEquals(federalState, result.get().getFederalState());
        assertEquals(office, result.get().getOffice());
        assertEquals(source, result.get().getSource());
        assertEquals(duration, result.get().getDuration());
        assertEquals(year, result.get().getYear());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should update an existing entry by incrementing its count and adding revenue")
    void testUpdateOrCreateStatistic_UpdateExistingEntry() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        int duration = 1;
        int year = 2024;
        double initialRevenue = 50.0;
        double additionalRevenue = 25.0;

        TaxesStatisticsView existingEntry = new TaxesStatisticsView();
        existingEntry.setFederalState(federalState);
        existingEntry.setOffice(office);
        existingEntry.setSource(source);
        existingEntry.setDuration(duration);
        existingEntry.setYear(year);
        existingEntry.setCount(5);
        existingEntry.setRevenue(initialRevenue);
        repository.save(existingEntry);

        // When
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, duration, year, additionalRevenue);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, source, duration, year);

        assertTrue(result.isPresent());
        assertEquals(6, result.get().getCount());
        assertEquals(initialRevenue + additionalRevenue, result.get().getRevenue());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should handle null office correctly")
    void testUpdateOrCreateStatistic_NullOffice() {
        // Given
        String federalState = "SH";
        String office = null;
        SubmissionType source = SubmissionType.ANALOG;
        int duration = 1;
        int year = 2024;
        double revenue = 50.0;

        // When
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, duration, year, revenue);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, source, duration, year);

        assertTrue(result.isPresent());
        assertEquals(1, result.get().getCount());
        assertEquals(revenue, result.get().getRevenue());
        assertNull(result.get().getOffice());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should handle different durations correctly")
    void testUpdateOrCreateStatistic_DifferentDurations() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        int year = 2024;
        double revenue = 50.0;

        // When
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, 1, year, revenue);
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, 3, year, revenue);
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, 5, year, revenue);

        // Then
        Optional<TaxesStatisticsView> result1 = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, source, 1, year);
        Optional<TaxesStatisticsView> result3 = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, source, 3, year);
        Optional<TaxesStatisticsView> result5 = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, source, 5, year);

        assertTrue(result1.isPresent());
        assertTrue(result3.isPresent());
        assertTrue(result5.isPresent());

        assertEquals(1, result1.get().getCount());
        assertEquals(1, result3.get().getCount());
        assertEquals(1, result5.get().getCount());

        assertEquals(revenue, result1.get().getRevenue());
        assertEquals(revenue, result3.get().getRevenue());
        assertEquals(revenue, result5.get().getRevenue());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should handle different sources correctly")
    void testUpdateOrCreateStatistic_DifferentSources() {
        // Given
        String federalState = "SH";
        String office = "office";
        int duration = 1;
        int year = 2024;
        double revenue = 50.0;

        // When
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, SubmissionType.ANALOG, duration, year, revenue);
        taxesStatisticsViewService.updateOrCreateStatistic(federalState, office, SubmissionType.ONLINE, duration, year, revenue);

        // Then
        Optional<TaxesStatisticsView> resultAnalog = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, SubmissionType.ANALOG, duration, year);
        Optional<TaxesStatisticsView> resultOnline = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                federalState, office, SubmissionType.ONLINE, duration, year);

        assertTrue(resultAnalog.isPresent());
        assertTrue(resultOnline.isPresent());

        assertEquals(1, resultAnalog.get().getCount());
        assertEquals(1, resultOnline.get().getCount());

        assertEquals(revenue, resultAnalog.get().getRevenue());
        assertEquals(revenue, resultOnline.get().getRevenue());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getStatisticsByFederalStateAndYears should return statistics for the specified federal state and years")
    void testGetStatisticsByFederalStateAndYears() {
        // Given
        String federalState = "SH";
        createTestData(federalState);

        // When
        List<TaxesStatisticsView> result = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, List.of(2023, 2024));

        // Then
        assertEquals(4, result.size());
        assertTrue(result.stream().allMatch(view -> view.getFederalState().equals(federalState)));
        assertTrue(result.stream().allMatch(view -> List.of(2023, 2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getStatisticsByOfficeAndYears should return statistics for the specified office and years")
    void testGetStatisticsByOfficeAndYears() {
        // Given
        String office = "office1";
        createTestData("SH", office);
        createTestData("NW", "office2");

        // When
        List<TaxesStatisticsView> result = taxesStatisticsViewService.getStatisticsByOfficeAndYears(office, List.of(2023, 2024));

        // Then
        assertEquals(4, result.size());
        assertTrue(result.stream().allMatch(view -> view.getOffice().equals(office)));
        assertTrue(result.stream().allMatch(view -> List.of(2023, 2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getStatisticsByYears should return statistics for the specified years")
    void testGetStatisticsByYears() {
        // Given
        createTestData("SH");
        createTestData("NW");

        // When
        List<TaxesStatisticsView> result = taxesStatisticsViewService.getStatisticsByYears(List.of(2023, 2024));

        // Then
        assertEquals(8, result.size());
        assertTrue(result.stream().allMatch(view -> List.of(2023, 2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getAvailableYears should return all distinct years in descending order")
    void testGetAvailableYears() {
        // Given
        createTestData("SH");
        createTestData("NW");

        TaxesStatisticsView entry2022 = new TaxesStatisticsView();
        entry2022.setFederalState("SH");
        entry2022.setOffice("office");
        entry2022.setSource(SubmissionType.ANALOG);
        entry2022.setDuration(1);
        entry2022.setYear(2022);
        entry2022.setCount(1);
        entry2022.setRevenue(50.0);
        repository.save(entry2022);

        // When
        List<Integer> result = taxesStatisticsViewService.getAvailableYears();

        // Then
        assertEquals(3, result.size());
        assertEquals(List.of(2024, 2023, 2022), result);
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.deleteAll should delete all entries")
    void testDeleteAll() {
        // Given
        createTestData("SH");
        createTestData("NW");

        // When
        taxesStatisticsViewService.deleteAll();

        // Then
        assertEquals(0, repository.count());
    }

    private void createTestData(String federalState) {
        createTestData(federalState, "office1");
    }

    private void createTestData(String federalState, String office) {
        // Create entries for 2023 with different durations
        TaxesStatisticsView entry2023_1 = new TaxesStatisticsView();
        entry2023_1.setFederalState(federalState);
        entry2023_1.setOffice(office);
        entry2023_1.setSource(SubmissionType.ANALOG);
        entry2023_1.setDuration(1);
        entry2023_1.setYear(2023);
        entry2023_1.setCount(1);
        entry2023_1.setRevenue(50.0);
        repository.save(entry2023_1);

        TaxesStatisticsView entry2023_3 = new TaxesStatisticsView();
        entry2023_3.setFederalState(federalState);
        entry2023_3.setOffice(office);
        entry2023_3.setSource(SubmissionType.ANALOG);
        entry2023_3.setDuration(3);
        entry2023_3.setYear(2023);
        entry2023_3.setCount(2);
        entry2023_3.setRevenue(150.0);
        repository.save(entry2023_3);

        // Create entries for 2024 with different durations
        TaxesStatisticsView entry2024_1 = new TaxesStatisticsView();
        entry2024_1.setFederalState(federalState);
        entry2024_1.setOffice(office);
        entry2024_1.setSource(SubmissionType.ONLINE);
        entry2024_1.setDuration(1);
        entry2024_1.setYear(2024);
        entry2024_1.setCount(3);
        entry2024_1.setRevenue(100.0);
        repository.save(entry2024_1);

        TaxesStatisticsView entry2024_5 = new TaxesStatisticsView();
        entry2024_5.setFederalState(federalState);
        entry2024_5.setOffice(office);
        entry2024_5.setSource(SubmissionType.ONLINE);
        entry2024_5.setDuration(5);
        entry2024_5.setYear(2024);
        entry2024_5.setCount(1);
        entry2024_5.setRevenue(250.0);
        repository.save(entry2024_5);
    }
}
