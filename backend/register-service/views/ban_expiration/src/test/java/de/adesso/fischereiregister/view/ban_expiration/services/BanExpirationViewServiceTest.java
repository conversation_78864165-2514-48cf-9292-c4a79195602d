package de.adesso.fischereiregister.view.ban_expiration.services;

import de.adesso.fischereiregister.view.ban_expiration.persistance.BanExpirationView;
import de.adesso.fischereiregister.view.ban_expiration.persistance.BanExpirationViewRepository;
import de.adesso.fischereiregister.view.ban_expiration.persistance.InMemoryBanExpirationViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class BanExpirationViewServiceTest {

    private BanExpirationViewService service;
    private BanExpirationViewRepository repository;

    @BeforeEach
    void setUp() {
        repository = new InMemoryBanExpirationViewRepository();
        service = new BanExpirationViewServiceImpl(repository);
    }

    @Test
    void testCreationWhenNoBanExists() {
        UUID registerEntryId = UUID.randomUUID();
        LocalDate expirationDate = LocalDate.of(2020, 1, 1);

        service.setBanExpiration(registerEntryId, expirationDate);

        Optional<BanExpirationView> banExpiration = repository.findById(registerEntryId);

        assertTrue(banExpiration.isPresent());
        assertEquals(banExpiration.get().getExpirationDate(), expirationDate);
    }

    @Test
    void testExpirationIsOverwrittenOnExistingBan() {
        UUID registerEntryId = UUID.randomUUID();
        LocalDate oldDate = LocalDate.of(2020, 1, 1);
        LocalDate newDate = oldDate.plusDays(1);

        BanExpirationView oldExpiration = new BanExpirationView();
        oldExpiration.setExpirationDate(oldDate);
        oldExpiration.setRegisterEntryId(registerEntryId);

        repository.save(oldExpiration);


        service.setBanExpiration(registerEntryId, newDate);


        Optional<BanExpirationView> newExpiration = repository.findById(registerEntryId);

        assertTrue(newExpiration.isPresent());
        assertEquals(newExpiration.get().getExpirationDate(), newDate);
    }

    @Test
    void testDeletionOnExistingExpiration() {
        UUID registerEntryId = UUID.randomUUID();
        LocalDate oldDate = LocalDate.of(2020, 1, 1);
        BanExpirationView oldExpiration = new BanExpirationView();
        oldExpiration.setExpirationDate(oldDate);
        oldExpiration.setRegisterEntryId(registerEntryId);

        repository.save(oldExpiration);


        service.deleteBanExpiration(registerEntryId);


        Optional<BanExpirationView> newExpiration = repository.findById(registerEntryId);

        assertTrue(newExpiration.isEmpty());
    }

    @Test
    void testDeletionOnNonExistingBanThrowsNoException() {
        UUID registerEntryId = UUID.randomUUID();

        service.deleteBanExpiration(registerEntryId);

        Optional<BanExpirationView> newExpiration = repository.findById(registerEntryId);

        assertTrue(newExpiration.isEmpty());
    }


}
