import "./WithPersonAndAddressInformationOS.tsp";
import "../../model/Fee.tsp";
import "../WithFederalState.tsp";
import "../WithConsentInformation.tsp";
import "./WithOnlineServiceContextOS.tsp";

model CreateLimitedLicenseRequestOS {
    
    @doc("Fee that is payed together with this request.")
    fee: Fee;

    @doc("License number connected to the register entry, if available.")
    licenseNumber?: string;

    @doc("Transaction id of the payment.")
    transactionId: string;

    @doc("URL to a secured disability certificate file. The URL should be secured and only accessible by authorized government officials.")
    disabilityCertificateFileURL: string;
    
    ...WithFederalState;
    ...WithOnlineServiceContextOS;
    ...WithPersonAndAddressInformationOS;
    ...WithConsentInformation;
}