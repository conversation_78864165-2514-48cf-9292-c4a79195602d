import "@typespec/http";
import "@typespec/openapi3";
import "../common.tsp";
import "../Fischereiregister.tsp";
import "../model/enum/LimitedLicenseApplicationStatus.tsp";
import "../response/postbox/LimitedLicenseApplicationSuccessResponse.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Postbox")
@route("postbox")
interface PostboxController {
    @doc("Fetches all limited license applications. If the applicationStatus is not set, all applications will be fetched.")
    @get()
    @route("limited-license-applications")
    getLimitedLicenseApplications(
        @doc("Which applications to fetch. May be left empty, if all applications should be fetched.")
        @query applicationStatus?: LimitedLicenseApplicationStatus,
    ): LimitedLicenseApplicationSuccessResponse | UnauthorizedResponse | ServerErrorResponse | ForbiddenResponse;
}
