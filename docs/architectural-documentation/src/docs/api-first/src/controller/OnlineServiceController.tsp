import "@typespec/http";
import "@typespec/openapi3";
import "../common.tsp";
import "../request/os/OrderFishingLicenseRequestOS.tsp";
import "../request/os/ReplaceFishingLicenseRequestOS.tsp";
import "../request/os/PayTaxRequestOS.tsp";
import "../request/os/CreateVacationLicenseRequestOS.tsp";
import "../request/os/ExtendVacationLicenseRequestOS.tsp";
import "../request/os/CreateLimitedLicenseRequestOS.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Online Service")
@route("/os")
interface OnlineServiceController {
  @route("v1/fishing-license")
  @doc("""
    Create a new fishing license on an already existing register entry.
    """)
  @post
  create(
    @body order: OrderFishingLicenseRequestOS,
  ): WithCreatedErrorsNoLocationNorBody;

  @route("v1/fishing-license:replace")
  @doc("""
    Replace a regular fishing license for a given register entry.
    """)
  @get
  replace(
    @body replaceOrder: ReplaceFishingLicenseRequestOS,
  ): WithStandardErrorsNoResponseContent;

  @route("v1/fishing-license/tax")
  @doc("Pay a tax to either an existing register entry or create a new register entry if no person matching the given person data could be found.")
  @put
  taxPayed(
    @body tax: PayTaxRequestOS,
  ): WithCreatedErrorsNoLocationNorBody;

  @route("v1/fishing-license/vacation")
  @post
  @doc("Add a vacation license to an existing register entry or create a new register entry if no person matching the given person data could be found.")
  vacationLicense(
    @body vacationLicense: CreateVacationLicenseRequestOS,
  ): WithCreatedErrorsNoLocationNorBody;

  @route("v1/fishing-license/vacation:extend")
    @put
    @doc("Extend an existing vacation license. If no vacation license exists the request will be rejected.")
    extendVacationLicense(
      @body vacationLicenseExtend: ExtendVacationLicenseRequestOS,
    ): WithCreatedErrorsNoLocationNorBody;

    @route("v1/fishing-license/limited")
    @put
    @doc("Regsiter an application for a limited license either on a new register entry or an existing one. The license-application has to be approved by a government official, in order to be converted to a full LIMITED type license..")
    limitedLicense(
      @body limitedLicense: CreateLimitedLicenseRequestOS,
    ): WithCreatedErrorsNoLocationNorBody;
}
