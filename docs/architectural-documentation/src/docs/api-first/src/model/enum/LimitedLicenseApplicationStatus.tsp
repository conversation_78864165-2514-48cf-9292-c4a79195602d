@doc("""
The status of a limited license application. The status is set after the application was created and after a government official has processed the application.

Successful applications are converted to limited licenses and the application itself is not fetchable anymore.
""")
enum LimitedLicenseApplicationStatus {
    @doc("""
    The application was not yet processed by a government official.
    """)
    PENDING,

    @doc("""
    The application was rejected by a government official. 
    
    If no data in the register entry is saved, the application and register entry will automatically deleted after a tenant specific period.
    """)
    REJECTED,
}