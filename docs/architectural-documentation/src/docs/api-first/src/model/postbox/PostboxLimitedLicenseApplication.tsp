import "../Person.tsp";
import "../enum/LimitedLicenseApplicationStatus.tsp";
import "../enum/FederalStateAbbreviation.tsp";

model PostboxLimitedLicenseApplication {
    @doc("The ID of the limited license application")
    id: string;

    @doc("The ID of the register entry. Note: The register entry might not be fully created and only person data is available.")
    registerId: string;

    @doc("Personal data of the person who applied for the limited license.")
    person: Person;

    @doc("The date when the application was created. Formatted as following: yyyy-MM-dd.")
    createdAt: string;

    @doc("The status of the limited license application.")
    applicationStatus: LimitedLicenseApplicationStatus;

    @doc("The federal state where the application was submitted.")
    federalState: FederalStateAbbreviation;
}