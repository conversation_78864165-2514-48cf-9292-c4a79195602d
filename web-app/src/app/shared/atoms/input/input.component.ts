import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Input,
  Output,
  ViewChild,
  computed,
  inject,
  input,
} from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

import { twMerge } from 'tailwind-merge';

import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { backgroundVariants, borderVariants, inputVariants, inputWrapperVariants } from '@/app/shared/atoms/input/input.component.styles';
import { InputType, InputVariant } from '@/app/shared/atoms/input/input.models';
import { TrimDirective } from '@/app/shared/directives/TrimDirective';
import { DateCutOffDirective } from '@/app/shared/directives/date-cut-off.directive';
import { IconWarningComponent } from '@/app/shared/icons/warning/warning.component';

/**
 * The Basic Input Component based on a given Form Control.
 * For an Input with labels to use in a form, please reference to the FormField component.
 */
@Component({
  standalone: true,
  selector: 'fish-input',
  templateUrl: './input.component.html',
  imports: [FormsModule, CommonModule, FocusRingComponent, ReactiveFormsModule, IconWarningComponent, TrimDirective, DateCutOffDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputComponent implements AfterViewInit {
  /** The Size of the input. In most cases, medium is what you want. Large is mainly used for search-bars. */
  @Input() public size: 'large' | 'medium' = 'medium';

  /** Placeholder text to display, when the Input is empty. */
  @Input() public placeholder: string = '';

  @Input() public type: InputType = 'text';

  @Input() public class: string = '';

  /** FormControl that is used for Validation or for value-tracking (in formGroups). */
  @Input({ required: true }) public control!: FormControl;

  @Input() public variant: InputVariant = 'default';

  /** The id is required to ensure accessibility */
  public readonly id = input.required<string>();

  @Output() public inputFocused = new EventEmitter<void>();

  @Output() public inputChanged = new EventEmitter<string>();

  /** Fired when enter key is pressed on input. */
  @Output() public submitted = new EventEmitter<string>();

  @ViewChild('inputElement') private readonly inputElement!: ElementRef;

  private readonly cdr: ChangeDetectorRef = inject(ChangeDetectorRef);

  protected readonly required = computed(() => {
    return this.control.hasValidator(Validators.required);
  });

  @HostBinding('class')
  protected get hostClass(): string {
    return twMerge(this.class, 'block w-full');
  }

  protected get borderClasses(): string {
    return twMerge(
      borderVariants({
        disabled: this.control.disabled,
        variant: this.variant,
      })
    );
  }

  protected get inputWrapperClasses(): string {
    return twMerge(
      inputWrapperVariants({
        disabled: this.control.disabled,
        size: this.size,
        variant: this.variant,
      })
    );
  }

  protected get inputClasses(): string {
    return twMerge(
      inputVariants({
        valid: !(this.control.touched && this.control.invalid),
        type: this.type,
      })
    );
  }

  protected get backgroundClasses(): string {
    return twMerge(
      backgroundVariants({
        disabled: this.control.disabled,
        variant: this.variant,
      })
    );
  }

  public ngAfterViewInit(): void {
    this.control.parent?.statusChanges.subscribe(() => this.cdr.detectChanges());
    this.control.statusChanges.subscribe(() => this.cdr.detectChanges());
  }

  public getInputNativeElement(): HTMLElement {
    return this.inputElement.nativeElement;
  }

  protected onFocus(): void {
    this.inputFocused.emit();
  }

  protected onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.inputChanged.emit(value);
  }
}
