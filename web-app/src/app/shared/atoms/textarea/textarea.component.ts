import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';

import { map, merge } from 'rxjs';
import { twMerge } from 'tailwind-merge';

import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { backgroundVariants, borderVariants, textareaVariants, textareaWrapperVariants } from '@/app/shared/atoms/textarea/textarea.component.styles';
import { TrimDirective } from '@/app/shared/directives/TrimDirective';
import { IconWarningComponent } from '@/app/shared/icons/warning/warning.component';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-textarea',
  standalone: true,
  imports: [FocusRingComponent, IconWarningComponent, ReactiveFormsModule, TrimDirective],
  templateUrl: './textarea.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextareaComponent {
  public readonly placeholder = input<string>('');

  public readonly class = input<string>('');

  public readonly control = input.required<FormControl>();

  /** The id is required to ensure accessibility */
  public readonly id = input.required<string>();

  public readonly textareaFocused = output<void>();

  public readonly textareaChanged = output<string>();

  public readonly submitted = output<string>();

  protected readonly required = computed(() => {
    return this.control().hasValidator(Validators.required);
  });

  protected readonly status = toComputed(() => {
    return merge(this.control().statusChanges, this.control().parent?.statusChanges || []).pipe(map(() => this.control().status));
  });

  protected readonly touched = toComputed(() => {
    return merge(this.control().statusChanges, this.control().parent?.statusChanges || []).pipe(map(() => this.control().touched));
  });

  protected readonly borderClasses = computed<string>(() => {
    return twMerge(
      borderVariants({
        disabled: this.status() === 'DISABLED',
      })
    );
  });

  protected readonly textareaWrapperClasses = computed<string>(() => {
    return twMerge(
      textareaWrapperVariants({
        disabled: this.status() === 'DISABLED',
      })
    );
  });

  protected readonly textareaClasses = computed<string>(() => {
    return twMerge(
      textareaVariants({
        valid: !(this.touched() && this.status() === 'INVALID'),
      })
    );
  });

  protected readonly backgroundClasses = computed<string>(() => {
    return twMerge(
      backgroundVariants({
        disabled: this.status() === 'DISABLED',
      })
    );
  });

  protected onFocus(): void {
    this.textareaFocused.emit();
  }

  protected onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.textareaChanged.emit(value);
  }
}
