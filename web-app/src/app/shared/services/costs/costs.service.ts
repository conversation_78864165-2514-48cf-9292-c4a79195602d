import { Injectable, inject } from '@angular/core';

import { TranslateService } from '@ngx-translate/core';
import { Observable, forkJoin, map } from 'rxjs';

import { LicenseDurationType, LicenseType } from '@digifischdok/ngx-register-sdk';

import { LicenseInformationStore } from '@/app/core/stores/license-information.store';
import { TaxInformationStore } from '@/app/core/stores/tax-information.store';
import { TaxCostOption } from '@/app/shared/services/costs/costs.models';

@Injectable({
  providedIn: 'root',
})
export class CostsService {
  private readonly taxCostOptions$: Observable<TaxCostOption[]>;

  private readonly taxInformationStore = inject(TaxInformationStore);

  private readonly licenseInformationStore = inject(LicenseInformationStore);

  constructor(private readonly translate: TranslateService) {
    this.taxCostOptions$ = forkJoin([
      this.translate.get('tax_cost_options.option_1'),
      this.translate.get('tax_cost_options.option_2'),
      this.translate.get('tax_cost_options.option_3'),
      this.translate.get('tax_cost_options.option_4'),
      this.translate.get('tax_cost_options.option_5'),
    ]).pipe(
      map(([option1, option2, option3, option4, option5]) => {
        const options = [option1, option2, option3, option4, option5];
        return options.map((option) => this.convertTranslatedTaxOption(option));
      }),
      map((options) => options.filter((option) => option !== null)),
      map((options) => options.map((option) => option as TaxCostOption))
    );
  }

  public calculateLicenseFee(selectedDuration: number | undefined, licenseType: LicenseType): number {
    const licenseInformation = this.licenseInformationStore.licenseInformationList()?.find((c) => c.licenseType === licenseType);

    if (!licenseInformation) {
      return 0;
    }

    if (
      licenseInformation.durationType === LicenseDurationType.LifeLong ||
      !licenseInformation.durationOptions ||
      licenseInformation.durationOptions.length === 1
    ) {
      return licenseInformation.feeAmount;
    }
    return licenseInformation.feeAmount * (selectedDuration ?? 1);
  }

  public calculateTaxCost(selectedDuration: number, officeFeeAlreadyPaid: boolean): number {
    const taxInformation = officeFeeAlreadyPaid ? this.taxInformationStore.taxWithoutOfficeFee() : this.taxInformationStore.taxWithOfficeFee();
    return taxInformation?.find((tax) => tax.duration === selectedDuration)?.price ?? 0;
  }

  public getTaxOptions$(): Observable<TaxCostOption[]> {
    return this.taxCostOptions$;
  }

  private convertTranslatedTaxOption(option: { [key: string]: string }): TaxCostOption | null {
    if (!option) {
      return null;
    }

    if (option['option_type'] === 'static') {
      const years = Number(option['years']);
      return {
        optionType: 'static',
        durationLabel: option['duration_label'],
        years,
      };
    } else if (option['option_type'] === 'stepper') {
      return {
        optionType: 'stepper',
        durationLabel: option['duration_label'],
        minYears: Number(option['min_years']),
        maxYears: Number(option['max_years']),
      };
    } else {
      return null;
    }
  }
}
