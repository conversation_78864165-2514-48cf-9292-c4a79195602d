import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, computed, inject, input, output, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { IdentificationDocument } from '@digifischdok/ngx-register-sdk';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { PaymentItemActionAreaComponent } from '@/app/shared/atoms/payment-item-action-area/payment-item-action-area.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { IconPrintComponent } from '@/app/shared/icons/print/print.component';
import { IconSendComponent } from '@/app/shared/icons/send/send.component';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';
import { NamingsService } from '@/app/shared/services/namings/namings.service';

@Component({
  selector: 'fish-document-item',
  standalone: true,
  imports: [
    ButtonComponent,
    PaymentItemActionAreaComponent,
    PaymentItemComponent,
    PaymentItemMainAreaComponent,
    TranslateModule,
    IconPrintComponent,
    IconDocumentPdfComponent,
    IconSendComponent,
    DatePipe,
  ],
  templateUrl: './document-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentItemComponent implements OnInit {
  // Inputs
  public readonly document = input.required<IdentificationDocument>();

  public readonly isFetchingDocument = input<boolean>(false);

  public readonly showSendButton = input<boolean>(false);

  //Outputs
  public readonly printDocumentButtonClicked = output<IdentificationDocument>();

  public readonly sendDocumentButtonClicked = output<IdentificationDocument>();

  // Dependencies
  private readonly namingsService = inject(NamingsService);

  private readonly translate = inject(TranslateService);

  // Fields
  private readonly taxDocumentTitle = toSignal(this.translate.get('document_item.category.tax'));

  private readonly limitedLicenseApprovalTitle = toSignal(this.translate.get('document_item.category.limited_license_approval'));

  private readonly licenseName = signal<string>('');

  protected readonly documentTitle = computed<string>(() => {
    const document = this.document();
    if (document.fishingLicense) {
      return this.licenseName();
    }

    if (document.tax) {
      return this.taxDocumentTitle();
    }

    if (document.limitedLicenseApproval) {
      return this.limitedLicenseApprovalTitle();
    }

    throw new Error('the Category of the Document could not be determined');
  });

  protected readonly documentFederalState = computed<string | undefined>(() => {
    if (this.document().fishingLicense) {
      return this.document().fishingLicense?.issuingFederalState ?? '';
    }
    if (this.document().tax) {
      return this.document().tax?.federalState ?? '';
    }
    if (this.document().limitedLicenseApproval) {
      return undefined;
    }
    throw new Error('the Federal State of the Document could not be determined');
  });

  public ngOnInit(): void {
    const license = this.document().fishingLicense;
    if (!license) {
      return;
    }

    this.namingsService.getLicenseNaming$(license.issuingFederalState, license.type).subscribe((name) => {
      this.licenseName.set(name);
    });
  }
}
