import { Type } from '@angular/core';
import { DefaultExport, Routes } from '@angular/router';

import { Observable } from 'rxjs';

import { AuthGuard } from '@/app/core/guards/auth.guard';
import { clearCitizenProfileGuard } from '@/app/core/guards/clear-citizen-profile.guard';
import { clearConsentGuard } from '@/app/core/guards/clear-consent.guard';
import { hasGivenConsentGuard } from '@/app/core/guards/has-given-consent.guard';
import { hasGivenMoveJurisdictionConsentGuard } from '@/app/core/guards/has-given-move-jurisdiction-consent.guard';
import { hasGivenTaxConsentGuard } from '@/app/core/guards/has-given-tax-consent.guard';
import { homepageGuard } from '@/app/core/guards/homepage.guard';
import { licenseExtendableGuard } from '@/app/core/guards/license-extendable-guard';
import { roleGuard } from '@/app/core/guards/role.guard';
import { validCallbackRouteGuard } from '@/app/core/guards/valid-callback-route.guard';
import { BasicLayoutComponent } from '@/app/core/layout/basic-layout/basic-layout.component';
import { HomepageLayoutComponent } from '@/app/core/layout/homepage-layout/homepage-layout.component';
import { WithCitizenInfoLayoutComponent } from '@/app/core/layout/with-citizen-info-layout/with-citizen-info-layout.component';
import { WithHeaderLayoutComponent } from '@/app/core/layout/with-header-layout/with-header-layout.component';
import { TitleResolver } from '@/app/core/resolvers/title.resolver';
import { UserRole } from '@/app/core/services/user/user.constants';
import { LoginPageComponent } from '@/app/features/auth/pages/login-page/login-page.component';
import { ConsentPageComponent } from '@/app/features/consent/pages/consent-page/consent-page.component';
import { MoveJurisdictionPreConsentPageComponent } from '@/app/features/consent/pages/move-jurisdiction-pre-consent-page/move-jurisdiction-pre-consent-page.component';
import { TaxConsentPageComponent } from '@/app/features/consent/pages/tax-consent-page/tax-consent-page.component';
import { CreatePassedExamPageComponent } from '@/app/features/create-passed-exam/pages/create-passed-exam-page/create-passed-exam-page.component';
import { DocumentsOverviewPageComponent } from '@/app/features/documents-overview/pages/documents-overview-page/documents-overview-page.component';
import { UnauthorizedGuard } from '@/app/features/error/guards/unauthorized.guard';
import { ErrorTimeoutPageComponent } from '@/app/features/error/pages/error-timeout-page/error-timeout-page.component';
import { NotFoundPageComponent } from '@/app/features/error/pages/not-found-page/not-found-page.component';
import { PostboxLicenseApplicationsPageComponent } from '@/app/features/postbox/pages/postbox-applications-page/postbox-license-applications-page.component';
import { AddLicensePageComponent } from '@/app/features/register-edit/pages/add-license-page/add-license-page.component';
import { AddLimitedLicensePageComponent } from '@/app/features/register-edit/pages/add-limited-license-page/add-limited-license-page.component';
import { AddVacationLicensePageComponent } from '@/app/features/register-edit/pages/add-vacation-license-page/add-vacation-license-page.component';
import { BanPersonPageComponent } from '@/app/features/register-edit/pages/ban-person-page/ban-person-page.component';
import { CreateLimitedLicensePageComponent } from '@/app/features/register-edit/pages/create-limited-license-page/create-limited-license-page.component';
import { CreatePersonPageComponent } from '@/app/features/register-edit/pages/create-person-page/create-person-page.component';
import { CreateVacationLicensePageComponent } from '@/app/features/register-edit/pages/create-vacation-license-page/create-vacation-license-page.component';
import { DigitizeLicensePageComponent } from '@/app/features/register-edit/pages/digitize-license-page/digitize-license-page.component';
import { ExtendVacationLicensePageComponent } from '@/app/features/register-edit/pages/extend-vacation-license-page/extend-vacation-license-page.component';
import { MoveJurisdictionPageComponent } from '@/app/features/register-edit/pages/move-jurisdiction-page/move-jurisdiction-page.component';
import { OrderCardPageComponent } from '@/app/features/register-edit/pages/order-card-page/order-card-page.component';
import { HomepageComponent } from '@/app/features/search/pages/homepage/homepage.component';
import { SearchResultsPageComponent } from '@/app/features/search/pages/search-results-page/search-results-page.component';
import { ServiceOverviewPageComponent } from '@/app/features/service-overview/pages/service-overview-page/service-overview-page.component';

import { PayTaxPageComponent } from './features/register-edit/pages/pay-tax-page/pay-tax-page.component';

type NotFoundPageComponentLoadComponent =
  | Type<unknown>
  | Observable<Type<unknown> | DefaultExport<Type<unknown>>>
  | Promise<Type<unknown> | DefaultExport<Type<unknown>>>;
type NotFoundPageComponentOfImport = typeof import('@/app/features/error/pages/not-found-page/not-found-page.component');

const NotFoundPageComponentImport: Promise<NotFoundPageComponentOfImport> = import(
  '@/app/features/error/pages/not-found-page/not-found-page.component'
);

export const routes: Routes = [
  {
    component: HomepageLayoutComponent,
    path: '',
    children: [
      {
        path: '',
        component: HomepageComponent,
        title: TitleResolver,
        canActivate: [AuthGuard, homepageGuard],
        data: {
          titleKey: 'home_page.title',
        },
      },
    ],
  },
  {
    path: 'consent',
    component: WithCitizenInfoLayoutComponent,
    children: [
      {
        path: '**',
        component: ConsentPageComponent,
        title: TitleResolver,
        data: {
          // this page solves its Title itself using the Angular Title Service, so it doesn't need the TitleResolver
          titleKey: 'consent.title',
          roles: [UserRole.Official],
        },
        canActivate: [AuthGuard, validCallbackRouteGuard, roleGuard],
        canDeactivate: [clearCitizenProfileGuard],
      },
    ],
  },
  {
    path: 'tax-consent',
    component: WithCitizenInfoLayoutComponent,
    children: [
      {
        path: '**',
        component: TaxConsentPageComponent,
        data: {
          // this page solves its Title itself using the Angular Title Service, so it doesn't need the TitleResolver
          titleKey: 'consent.title',
          roles: [UserRole.Official],
        },
        canActivate: [AuthGuard, validCallbackRouteGuard, roleGuard],
        canDeactivate: [clearCitizenProfileGuard],
      },
    ],
  },
  {
    path: 'move-jurisdiction-pre-consent/:registerEntryId',
    component: WithCitizenInfoLayoutComponent,
    title: TitleResolver,
    data: {
      titleKey: 'move_jurisdiction_consent.title',
    },
    children: [
      {
        path: '',
        component: MoveJurisdictionPreConsentPageComponent,
        canActivate: [AuthGuard, roleGuard],
        data: {
          roles: [UserRole.Official],
        },
      },
    ],
  },
  {
    path: 'create-passed-exam',
    component: BasicLayoutComponent,
    title: TitleResolver,
    data: {
      titleKey: 'passed_exam_create.title',
      roles: [UserRole.ExamDataCreator],
    },
    canActivate: [AuthGuard],
    canActivateChild: [roleGuard],
    children: [
      {
        component: CreatePassedExamPageComponent,
        path: '',
      },
    ],
  },
  {
    path: 'digitize-license',
    component: WithHeaderLayoutComponent,
    title: TitleResolver,
    data: {
      titleKey: 'digitize.title',
    },
    children: [
      {
        component: DigitizeLicensePageComponent,
        path: '',
        data: {
          roles: [UserRole.Official],
        },
        canActivate: [AuthGuard, hasGivenConsentGuard, roleGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
    ],
  },
  {
    path: 'create-person',
    component: WithCitizenInfoLayoutComponent,
    title: TitleResolver,
    data: {
      titleKey: 'person_tax_create.title',
    },
    children: [
      {
        component: CreatePersonPageComponent,
        path: '',
        data: {
          roles: [UserRole.Official],
        },
        canActivate: [AuthGuard, hasGivenTaxConsentGuard, roleGuard],
        canDeactivate: [clearConsentGuard],
      },
    ],
  },
  {
    path: 'create-vacation-license',
    component: WithCitizenInfoLayoutComponent,
    title: TitleResolver,
    data: {
      titleKey: 'create_vacation_license.title',
    },
    children: [
      {
        component: CreateVacationLicensePageComponent,
        path: '',
        data: {
          roles: [UserRole.Official],
        },
        canActivate: [AuthGuard, hasGivenConsentGuard, roleGuard],
        canDeactivate: [clearConsentGuard],
      },
    ],
  },
  {
    path: 'create-limited-license',
    component: WithCitizenInfoLayoutComponent,
    title: TitleResolver,
    data: {
      titleKey: 'create_limited_license.title',
    },
    children: [
      {
        component: CreateLimitedLicensePageComponent,
        path: '',
        data: {
          roles: [UserRole.Official, UserRole.LimitedLicenseCreator],
        },
        canActivate: [AuthGuard, hasGivenConsentGuard, roleGuard],
        canDeactivate: [clearConsentGuard],
      },
    ],
  },
  {
    component: BasicLayoutComponent,
    path: 'register-entries',
    children: [
      {
        component: SearchResultsPageComponent,
        path: '',
        title: TitleResolver,
        canActivate: [AuthGuard, roleGuard],
        data: {
          titleKey: 'search.title',
          roles: [UserRole.Official],
        },
      },
    ],
  },
  {
    component: WithCitizenInfoLayoutComponent,
    path: 'register-entries/:registerEntryId',
    data: {
      roles: [UserRole.Official],
    },
    canActivateChild: [roleGuard],
    children: [
      {
        component: ServiceOverviewPageComponent,
        path: '',
        title: TitleResolver,
        canActivate: [AuthGuard],
        canDeactivate: [clearCitizenProfileGuard, clearConsentGuard],
        data: {
          titleKey: 'service_overview.title',
        },
      },
      {
        path: 'add-license',
        title: TitleResolver,
        data: {
          titleKey: 'add_license.title',
        },
        component: AddLicensePageComponent,
        canActivate: [AuthGuard, hasGivenConsentGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
      {
        path: 'add-vacation-license',
        title: TitleResolver,
        data: {
          titleKey: 'create_vacation_license.title',
        },
        component: AddVacationLicensePageComponent,
        canActivate: [AuthGuard, hasGivenConsentGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
      {
        path: 'extend-vacation-license/:fishingLicenseNumber',
        title: TitleResolver,
        data: {
          titleKey: 'extend_vacation_license.title',
        },
        component: ExtendVacationLicensePageComponent,
        canActivate: [AuthGuard, licenseExtendableGuard, hasGivenConsentGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
      {
        path: 'add-limited-license',
        title: TitleResolver,
        data: {
          titleKey: 'create_limited_license.title',
          roles: [UserRole.Official, UserRole.LimitedLicenseCreator],
        },
        component: AddLimitedLicensePageComponent,
        canActivate: [AuthGuard, hasGivenConsentGuard, roleGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
      {
        path: 'pay-tax',
        title: TitleResolver,
        data: {
          titleKey: 'pay_tax.title',
        },
        component: PayTaxPageComponent,
        canActivate: [AuthGuard, hasGivenTaxConsentGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
      {
        path: 'ban-person',
        title: TitleResolver,
        data: {
          titleKey: 'ban_person.title',
          roles: [UserRole.BanManager],
        },
        component: BanPersonPageComponent,
        canActivate: [AuthGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
      },
      {
        path: 'order-card/:fishingLicenseNumber',
        component: OrderCardPageComponent,
        canActivate: [AuthGuard, hasGivenConsentGuard],
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
        title: TitleResolver,
        data: {
          titleKey: 'order_card.title',
        },
      },
      {
        path: 'move-jurisdiction',
        component: MoveJurisdictionPageComponent,
        title: TitleResolver,
        canActivate: [AuthGuard, hasGivenMoveJurisdictionConsentGuard],
        canDeactivate: [clearCitizenProfileGuard, clearConsentGuard],
        data: {
          titleKey: 'move_jurisdiction.title',
        },
      },
      {
        path: 'documents',
        component: DocumentsOverviewPageComponent,
        title: TitleResolver,
        canActivate: [AuthGuard],
        canDeactivate: [clearCitizenProfileGuard],
        data: {
          titleKey: 'documents_overview.title',
        },
      },
    ],
  },
  {
    component: HomepageLayoutComponent,
    path: 'postbox',
    canActivate: [AuthGuard, roleGuard],
    data: {
      roles: [UserRole.LimitedLicenseCreator, UserRole.Official],
    },
    children: [
      {
        path: 'limited-license-applications',
        title: TitleResolver,
        component: PostboxLicenseApplicationsPageComponent,
        data: {
          titleKey: 'postbox.applications.title',
        },
      },
    ],
  },
  {
    component: ErrorTimeoutPageComponent,
    path: 'error-timeout',
    title: 'DigiFischDok - Zugang Abgelaufen',
    canActivate: [UnauthorizedGuard],
  },
  {
    path: 'not-found',
    component: BasicLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        canDeactivate: [clearConsentGuard, clearCitizenProfileGuard],
        loadComponent: (): NotFoundPageComponentLoadComponent =>
          NotFoundPageComponentImport.then((m: NotFoundPageComponentOfImport): typeof NotFoundPageComponent => m.NotFoundPageComponent),
        title: TitleResolver,
        data: {
          titleKey: 'error.not_found.title',
        },
      },
    ],
  },
  {
    // Login page does not need a layout
    path: 'login',
    component: LoginPageComponent,
    title: 'DigiFischDok - Login',
    canActivate: [AuthGuard],
  },
  {
    path: 'keycloak/login',
    component: LoginPageComponent,
    title: 'DigiFischDok - Login',
    canActivate: [AuthGuard],
  },
  {
    path: '**',
    redirectTo: '/not-found',
  },
];
