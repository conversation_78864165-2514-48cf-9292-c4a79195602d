<div class="flex flex-col bg-background-2 px-3 pb-4 pt-2">
  <table class="w-full text-left">
    <caption></caption>
    <thead>
      <tr>
        <th class="font-semibold px-3 py-1">
          <span [innerText]="'statistics.submission_type_table.attribute' | translate"></span>
        </th>
        <th class="font-semibold px-3 py-1 text-right">
          <span [innerText]="yearData()?.year"></span>
        </th>
        <th class="font-semibold px-3 py-1 text-right text-font-secondary">
          <span [innerText]="previousYearData()?.year"></span>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr class="border-b border-border-divider">
        <td class="flex items-center gap-2 px-1 pb-3 pt-1">
          <fish-icon-own-state class="-my-2" size="32"></fish-icon-own-state>
          <span [innerText]="'statistics.submission_type_table.general' | translate"></span>
        </td>
        <td class="px-3 py-1 text-right">
          <span [innerText]="yearData()?.online + yearData()?.analog | number"></span>
        </td>
        <td class="px-3 py-1 text-right text-font-secondary">
          <span [innerText]="previousYearData()?.online + previousYearData()?.analog | number"></span>
        </td>
      </tr>
      <tr>
        <td class="flex items-center gap-2 px-1 pb-1 pt-3">
          <fish-icon-e-mail class="-my-2" size="32"></fish-icon-e-mail>
          <span [innerText]="'statistics.submission_type_table.online' | translate"></span>
        </td>
        <td class="px-3 pb-1 pt-3 text-right">
          <span [innerText]="yearData()?.online | number"></span>
        </td>
        <td class="px-3 pb-1 pt-3 text-right text-font-secondary">
          <span [innerText]="previousYearData()?.online | number"></span>
        </td>
      </tr>
      <tr>
        <td class="flex items-center gap-2 px-1 py-1">
          <fish-icon-authority class="-my-2" size="32"></fish-icon-authority>
          <span [innerText]="'statistics.submission_type_table.analog' | translate"></span>
        </td>
        <td class="px-3 py-1 text-right">
          <span [innerText]="yearData()?.analog | number"></span>
        </td>
        <td class="px-3 py-1 text-right text-font-secondary">
          <span [innerText]="previousYearData()?.analog | number"></span>
        </td>
      </tr>
    </tbody>
  </table>
</div>
