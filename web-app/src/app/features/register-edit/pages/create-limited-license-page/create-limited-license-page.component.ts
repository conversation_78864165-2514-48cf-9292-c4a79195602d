import { ChangeDetectionStrategy, Component, inject } from '@angular/core';

import { TranslateService } from '@ngx-translate/core';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { CreateLimitedLicenseTabGroupComponent } from '@/app/features/register-edit/components/templates/create-limited-license-tab-group/create-limited-license-tab-group.component';

@Component({
  selector: 'fish-create-limited-license-page',
  standalone: true,
  imports: [PageContentComponent, CreateLimitedLicenseTabGroupComponent],
  templateUrl: './create-limited-license-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateLimitedLicensePageComponent {
  private readonly profileHeaderStore: ProfileHeaderStore = inject(ProfileHeaderStore);

  private readonly translate: TranslateService = inject(TranslateService);

  constructor() {
    this.translate.get('create_limited_license.title').subscribe((text: string): void => this.profileHeaderStore.setText(text));
  }
}
