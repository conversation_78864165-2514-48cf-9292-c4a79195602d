import { Injectable } from '@angular/core';

import { ValidityPeriod } from '@digifischdok/ngx-register-sdk';

@Injectable({
  providedIn: 'root',
})
export class RequestValidityPeriodBuilderService {
  public buildValidityPeriod(
    validityPeriod: Partial<{
      validFrom: string | null;
      validTo: string | null;
    }>
  ): ValidityPeriod {
    const { validFrom, validTo } = validityPeriod;
    if (!validFrom) {
      throw new Error('validFrom is required');
    }
    return { validFrom, validTo: validTo ?? undefined };
  }
}
