import { Injectable } from '@angular/core';

import { LimitedLicenseApproval } from '@digifischdok/ngx-register-sdk';

import { LimitedLicenseApprovalFormValues } from '@/app/features/register-edit/components/organisms/limited-license-approval-form/limited-license-approval-form.models';

@Injectable({
  providedIn: 'root',
})
export class LimitedLicenseApprovalBuilderService {
  public buildLimitedLicenseApproval(limitedLicenseApprovalFormValues: LimitedLicenseApprovalFormValues): LimitedLicenseApproval {
    const { signingEmployee, processData } = limitedLicenseApprovalFormValues;
    if (!signingEmployee) {
      throw new Error('Tried submitting illegal form state. Signing employee was undefined.');
    }
    if (!processData) {
      throw new Error('Tried submitting illegal form state. Process data was undefined.');
    }
    return {
      signingEmployee: {
        personalSign: signingEmployee.personalSign ?? '',
        name: signingEmployee.name ?? '',
        email: signingEmployee.email ?? '',
        phone: signingEmployee.phone ?? '',
      },
      createdAt: processData.createdAt ?? '',
      fileNumber: processData.fileNumber ?? '',
      cashRegisterSign: processData.cashRegisterSign ?? '',
      justificationForLimitedDurationNotice: processData.justificationForLimitedDurationNotice ?? '',
    };
  }
}
