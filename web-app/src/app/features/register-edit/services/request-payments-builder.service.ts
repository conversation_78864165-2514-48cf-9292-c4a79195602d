import { Injectable, inject } from '@angular/core';

import { Fee, LicenseType, PaymentType, Tax } from '@digifischdok/ngx-register-sdk';

import { UserService } from '@/app/core/services/user/user.service';
import { PreviousPaymentsFormValues } from '@/app/features/register-edit/components/organisms/previous-payments-form/previous-payments-form.models';
import { PaymentsStepFormValues } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.models';
import { CostsService } from '@/app/shared/services/costs/costs.service';

@Injectable({
  providedIn: 'root',
})
export class RequestPaymentsBuilderService {
  private readonly costsService = inject(CostsService);

  private readonly userService = inject(UserService);

  public buildPreviousTaxes(formValue: PreviousPaymentsFormValues | undefined): Tax[] {
    if (!formValue?.validTo) {
      return [];
    }

    const currentYear = new Date().getFullYear();
    const startOfCurrentYear = Date.UTC(formValue.validFrom ?? currentYear, 0, 1);
    const validFrom = this.formatDate(new Date(startOfCurrentYear));

    const endOfValidationPeriod = Date.UTC(formValue.validTo ?? currentYear, 11, 31);
    const validTo = this.formatDate(new Date(endOfValidationPeriod));

    return [
      {
        validFrom,
        validTo,
        federalState: this.userService.getFederalState(),
        paymentInfo: {
          type: PaymentType.Card,
          amount: 0,
        },
      },
    ];
  }

  public buildFees(
    licenseType: LicenseType,
    paymentsStepFormValues: PaymentsStepFormValues,
    validityPeriod?: Partial<{
      validFrom: string | null;
      validTo: string | null;
    }>
  ): Fee[] {
    if (!paymentsStepFormValues?.paymentMethod) {
      throw new Error('Invalid form status. Payment method must not be empty');
    }

    const validTo = validityPeriod?.validTo ?? undefined;

    const validFrom = validityPeriod?.validFrom ?? this.formatDate(new Date());
    const duration = validTo && validFrom ? new Date(validTo).getTime() - new Date(validFrom).getTime() + 1 : 1;
    const federalState = this.userService.getFederalState();
    const amount = this.costsService.calculateLicenseFee(duration, licenseType);
    const type = paymentsStepFormValues.paymentMethod as PaymentType;

    return [
      {
        validFrom,
        validTo,
        federalState,
        paymentInfo: {
          type,
          amount,
        },
      },
    ];
  }

  public buildTaxes(formValues: PaymentsStepFormValues): Tax[] {
    const { taxSelection, paymentMethod } = formValues;

    if (!paymentMethod) {
      return [];
    }

    if (!taxSelection) {
      return [];
    }

    const startDate = new Date(Date.UTC(taxSelection.yearFrom));
    const validFrom = this.formatDate(startDate);

    let validTo: string | undefined;
    if (taxSelection.yearTo !== null) {
      const endDate = new Date(Date.UTC(taxSelection.yearTo, 11, 31));
      validTo = this.formatDate(endDate);
    } else {
      validTo = undefined;
    }

    return [
      {
        validFrom,
        validTo,
        federalState: this.userService.getFederalState(),
        paymentInfo: {
          type: paymentMethod,
          amount: taxSelection.cost,
        },
      },
    ];
  }

  private formatDate(date: Date): string {
    return date.toISOString().substring(0, 10);
  }
}
