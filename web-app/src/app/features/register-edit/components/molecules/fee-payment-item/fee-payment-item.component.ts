import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { LicenseType } from '@digifischdok/ngx-register-sdk';

import { CheckboxComponent } from '@/app/shared/atoms/checkbox/checkbox.component';
import { PaymentItemActionAreaComponent } from '@/app/shared/atoms/payment-item-action-area/payment-item-action-area.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';
import { FormatCurrencyPipe } from '@/app/shared/pipes/format-currency.pipe';

@Component({
  selector: 'fish-fee-payment-item',
  standalone: true,
  imports: [
    PaymentItemComponent,
    PaymentItemMainAreaComponent,
    PaymentItemActionAreaComponent,
    CheckboxComponent,
    TranslateModule,
    FormatCurrencyPipe,
  ],
  templateUrl: './fee-payment-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeePaymentItemComponent {
  public readonly amount = input<number | null>();

  public readonly licenseType = input<LicenseType>(LicenseType.Regular);

  public readonly validTo = input<string | undefined>(undefined);

  protected readonly validToYear = computed(() => {
    return this.validTo() ? new Date(this.validTo()!).getFullYear() : undefined;
  });

  protected readonly checkboxControl: FormControl<boolean | null>;

  protected readonly titleKey = computed(() => {
    return `namings.licenses.${this.licenseType().toLowerCase()}`;
  });

  constructor(formBuilder: FormBuilder) {
    this.checkboxControl = formBuilder.control({
      value: true,
      disabled: true,
    });
  }
}
