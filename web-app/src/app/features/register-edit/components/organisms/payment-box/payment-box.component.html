<fish-card>
  <fish-card-header [cost]="totalAmount()" class="text-l" variant="primary" data-testid="payment-box-cost">
    <fish-icon-money icon size="64"></fish-icon-money>
    {{ 'edit_form.payments.box_title' | translate }}
  </fish-card-header>

  <fish-card-content>
    <div class="flex flex-col items-stretch gap-4">
      @if (showFee()) {
        <fish-fee-payment-item
          [amount]="feeCost()"
          [licenseType]="licenseType()"
          [validTo]="licenseValidTo()"
          data-testid="payment-box-fee"
        ></fish-fee-payment-item>
      }

      @if (showPaidTaxes()) {
        <fish-payed-taxes-payment-item
          [showSubtextForNone]="previouslyPaidTax() === null"
          [yearFrom]="previouslyPaidTax()?.yearFrom ?? null"
          [yearTo]="previouslyPaidTax()?.yearTo ?? null"
          data-testid="payment-box-payed-taxes"
        />
      }

      <fish-tax-payment-item
        [taxSelectionControl]="formGroup.controls.taxSelection"
        [costOptions]="taxCostOptions()"
        [isTaxOptional]="isTaxOptional()"
        [officeFeeAlreadyPaid]="showFee()"
        data-testid="payment-box-tax-payment-item"
      ></fish-tax-payment-item>
    </div>
  </fish-card-content>
</fish-card>
