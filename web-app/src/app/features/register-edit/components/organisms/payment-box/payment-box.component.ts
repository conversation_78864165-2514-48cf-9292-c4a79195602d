import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, ViewChild, computed, inject, input, signal } from '@angular/core';
import { FormBuilder } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { LicenseType } from '@digifischdok/ngx-register-sdk';

import { FeePaymentItemComponent } from '@/app/features/register-edit/components/molecules/fee-payment-item/fee-payment-item.component';
import { PayedTaxesPaymentItemComponent } from '@/app/features/register-edit/components/molecules/payed-taxes-payment-item/payed-taxes-payment-item.component';
import { PaymentBoxFormGroup } from '@/app/features/register-edit/components/organisms/payment-box/payment-box.models';
import { TaxPaymentItemComponent } from '@/app/features/register-edit/components/organisms/tax-payment-item/tax-payment-item.component';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { FishBeforeUnloadAndCanDeactivate } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.decorator';
import { IconMoneyComponent } from '@/app/shared/icons/money/money.component';
import { TaxSelection } from '@/app/shared/models/tax-selection';
import { CardComponent } from '@/app/shared/molecules/card/card.component';
import { TaxCostOption } from '@/app/shared/services/costs/costs.models';
import { CostsService } from '@/app/shared/services/costs/costs.service';

@FishBeforeUnloadAndCanDeactivate()
@Component({
  selector: 'fish-payment-box',
  standalone: true,
  imports: [
    CardComponent,
    IconMoneyComponent,
    CardHeaderComponent,
    TranslateModule,
    CardContentComponent,
    FeePaymentItemComponent,
    TaxPaymentItemComponent,
    PayedTaxesPaymentItemComponent,
  ],
  templateUrl: './payment-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentBoxComponent extends FormComponent<PaymentBoxFormGroup> implements OnInit, AfterViewInit {
  // Inputs
  public readonly showPreviouslyPayedTax = input<boolean>(false);

  public readonly isTaxCheckboxDisabled = input<boolean>(false);

  public readonly licenseType = input<LicenseType>(LicenseType.Regular);

  public readonly licenseValidTo = input<string>();

  public readonly showFee = input<boolean>(true);

  public readonly isTaxOptional = input<boolean>(false);

  public readonly feeCost = input<number>(0);

  public readonly previouslyPaidTax = input<TaxSelection | null>(null);

  public readonly taxCostOptions = input<TaxCostOption[]>([]);

  // Fields
  private readonly taxCost = signal<number>(0);

  protected readonly totalAmount = computed<number>(() => this.taxCost() + (this.showFee() ? this.feeCost() : 0));

  protected readonly showPaidTaxes = computed<boolean>(() => {
    return this.previouslyPaidTax() !== null || this.showPreviouslyPayedTax();
  });

  @ViewChild(TaxPaymentItemComponent) private readonly taxPaymentItem!: TaxPaymentItemComponent;

  public override formGroup!: PaymentBoxFormGroup;

  // Dependencies
  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  private readonly costsService: CostsService = inject(CostsService);

  constructor() {
    super();
    this.initFormGroup();
    this.captureInitialState();
  }

  public override ngOnInit(): void {
    super.ngOnInit();
    this.initTaxCost();
  }

  public ngAfterViewInit(): void {
    if (this.isTaxCheckboxDisabled()) {
      this.disableTaxCheckbox();
      this.setDefaultTax();
    }
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      taxSelection: this.formBuilder.control<TaxSelection | null>(null),
    });
    this.formGroup.controls.taxSelection.valueChanges.subscribe(() => this.formGroup.updateValueAndValidity());
  }

  private setDefaultTax(): void {
    const cost = this.costsService.calculateTaxCost(1, this.showFee());
    const currentYear = new Date().getFullYear();
    this.formGroup.controls.taxSelection.setValue({
      cost: cost ?? 0,
      yearFrom: currentYear,
      yearTo: currentYear,
    });
    this.captureInitialState();
    this.taxPaymentItem.isSelectedControl.setValue(true, { emitEvent: false });
  }

  private disableTaxCheckbox(): void {
    this.taxPaymentItem.isSelectedControl.disable({ emitEvent: false });
  }

  private initTaxCost(): void {
    this.taxCost.set(this.formGroup.controls.taxSelection.value?.cost ?? 0);
    this.formGroup.controls.taxSelection.valueChanges.subscribe((tax) => {
      this.taxCost.set(tax?.cost ?? 0);
    });
  }
}
