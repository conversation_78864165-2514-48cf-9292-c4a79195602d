import { ChangeDetectionStrategy, Component, ViewChild, computed, inject, input, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, retry } from 'rxjs';

import {
  IdentificationDocument,
  IdentificationDocumentType,
  IdentificationDocumentsMailTemplateType,
  IdentificationDocumentsService,
  LicenseType,
} from '@digifischdok/ngx-register-sdk';

import { SendDocumentsItemComponent } from '@/app/features/register-edit/components/molecules/send-documents-item/send-documents-item.component';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { CardSectionComponent } from '@/app/shared/atoms/card-section/card-section.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';
import { DocumentItemComponent } from '@/app/shared/molecules/document-item/document-item.component';
import { SendDocumentsDialogComponent } from '@/app/shared/organisms/send-documents-dialog/send-documents-dialog.component';
import { SendDocumentsSuccessFeedbackComponent } from '@/app/shared/organisms/send-documents-success-feedback/send-documents-success-feedback.component';
import { DocumentOpenerService } from '@/app/shared/services/document-opener.service';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-documents-box',
  standalone: true,
  imports: [
    CardComponent,
    CardHeaderComponent,
    CardContentComponent,
    CardSectionComponent,
    IconDocumentPdfComponent,
    TranslateModule,
    DocumentItemComponent,
    SendDocumentsDialogComponent,
    SendDocumentsItemComponent,
    SendDocumentsSuccessFeedbackComponent,
  ],
  templateUrl: './documents-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsBoxComponent {
  // Inputs
  public readonly registerEntryId = input.required<string>();

  public readonly documents = input.required<IdentificationDocument[]>();

  public readonly identificationDocumentsMailTemplateType = input<IdentificationDocumentsMailTemplateType>();

  public readonly showMailSection = input<boolean>(true);

  // Fields
  protected isSendingDocument = signal<boolean>(false);

  protected documentToFetch = signal<IdentificationDocument | undefined>(undefined);

  protected readonly pdfDocuments = computed<IdentificationDocument[]>(() =>
    this.documents().filter((document) => document.type === IdentificationDocumentType.Pdf)
  );

  @ViewChild(SendDocumentsDialogComponent)
  private readonly sendDocumentsDialog!: SendDocumentsDialogComponent;

  @ViewChild(SendDocumentsSuccessFeedbackComponent)
  private readonly sendDocumentsSuccessFeedback!: SendDocumentsSuccessFeedbackComponent;

  // Dependencies
  private readonly identificationDocumentsServiceApi: IdentificationDocumentsService = inject(IdentificationDocumentsService);

  private readonly documentOpener: DocumentOpenerService = inject(DocumentOpenerService);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  protected fetchDocument(document: IdentificationDocument): void {
    if (!document) {
      throw new Error('Tried fetching a document but none was selected');
    }
    this.documentToFetch.set(document);
    this.documentOpener.openDocument$(this.registerEntryId(), document).subscribe({
      complete: () => this.documentToFetch.set(undefined),
    });
  }

  protected sendDocuments(email: string): void {
    const templateType = this.identificationDocumentsMailTemplateType();
    if (!templateType) {
      throw new Error('Tried sending documents but no template type was selected');
    }
    // this is an edge case: if the user has a vacation license and a tax document, we need to remove the tax document from the list of documents to send
    const hasVacationLicense = this.pdfDocuments().some((document) => document.fishingLicense?.type === LicenseType.Vacation);

    const filteredDocuments = this.pdfDocuments().filter((document) => {
      return !(hasVacationLicense && document.tax);
    });
    const documentIds = filteredDocuments.map((value) => value.documentId);
    if (!documentIds) {
      throw new Error('Tried sending documents but none was selected');
    }
    this.isSendingDocument.set(true);
    this.identificationDocumentsServiceApi
      .identificationDocumentsControllerSendMail(this.registerEntryId(), documentIds, email, templateType)
      .pipe(
        retry({ count: 2, delay: 200 }),
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => {
          this.isSendingDocument.set(false);
        })
      )
      .subscribe(() => {
        this.sendDocumentsDialog.close();
        this.sendDocumentsSuccessFeedback.open();
      });
  }
}
