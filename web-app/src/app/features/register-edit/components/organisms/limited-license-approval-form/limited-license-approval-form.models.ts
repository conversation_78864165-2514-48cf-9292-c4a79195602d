import { FormGroup } from '@angular/forms';

import { LimitedLicenseProcessDataFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-process-data-form/limited-license-process-data-form.models';
import { LimitedLicenseSigningEmployeeFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-signing-employee-form/limited-license-signing-employee-form.models';

export type LimitedLicenseApprovalForm = {
  signingEmployee: LimitedLicenseSigningEmployeeFormGroup;
  processData: LimitedLicenseProcessDataFormGroup;
};

export type LimitedLicenseApprovalFormGroup = FormGroup<LimitedLicenseApprovalForm>;

export type LimitedLicenseApprovalFormValues = LimitedLicenseApprovalFormGroup['value'];
