import { AfterViewInit, ChangeDetectionStrategy, Component, inject, input, viewChild } from '@angular/core';
import { FormBuilder } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { LimitedLicenseApprovalFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-approval-form/limited-license-approval-form.models';
import { LimitedLicenseProcessDataFormComponent } from '@/app/features/register-edit/components/organisms/limited-license-process-data-form/limited-license-process-data-form.component';
import { LimitedLicenseSigningEmployeeFormComponent } from '@/app/features/register-edit/components/organisms/limited-license-signing-employee-form/limited-license-signing-employee-form.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';

@Component({
  selector: 'fish-limited-license-approval-form',
  standalone: true,
  imports: [TranslateModule, LimitedLicenseSigningEmployeeFormComponent, LimitedLicenseProcessDataFormComponent],
  templateUrl: './limited-license-approval-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseApprovalFormComponent extends FormComponent<LimitedLicenseApprovalFormGroup> implements AfterViewInit {
  public readonly requireJustificationForLimitedDurationNotice = input<boolean>(false);

  public override formGroup!: LimitedLicenseApprovalFormGroup;

  protected readonly signingEmployeeForm = viewChild.required(LimitedLicenseSigningEmployeeFormComponent);

  protected readonly processDataForm = viewChild.required(LimitedLicenseProcessDataFormComponent);

  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  public ngAfterViewInit(): void {
    this.initFormGroup();
    this.captureInitialState();
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      signingEmployee: this.signingEmployeeForm().formGroup,
      processData: this.processDataForm().formGroup,
    }) as LimitedLicenseApprovalFormGroup;
  }

  public override validate(): void {
    this.signingEmployeeForm().formGroup.markAllAsTouched();
    this.signingEmployeeForm().formGroup.updateValueAndValidity();
    this.processDataForm().formGroup.markAllAsTouched();
    this.processDataForm().formGroup.updateValueAndValidity();
  }
}
