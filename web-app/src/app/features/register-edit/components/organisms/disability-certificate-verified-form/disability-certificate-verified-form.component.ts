import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { ConsentStore } from '@/app/core/stores/consent.store';
import { DisabilityCertificateVerifiedFormGroup } from '@/app/features/register-edit/components/organisms/disability-certificate-verified-form/disability-certificate-verified-form.models';
import { CheckboxComponent } from '@/app/shared/atoms/checkbox/checkbox.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';

@Component({
  selector: 'fish-disability-certificate-verified-form',
  standalone: true,
  imports: [CheckboxComponent, TranslateModule],
  templateUrl: './disability-certificate-verified-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DisabilityCertificateVerifiedFormComponent extends FormComponent<DisabilityCertificateVerifiedFormGroup> implements OnInit {
  // FIELDS
  public override formGroup!: DisabilityCertificateVerifiedFormGroup;

  // DEPENDENCIES
  private readonly formBuilder = inject(FormBuilder);

  private readonly consentStore = inject(ConsentStore);

  public override ngOnInit(): void {
    this.initFormGroup();
  }

  private initFormGroup() {
    this.formGroup = this.formBuilder.group({
      disabilityCertificateVerified: this.formBuilder.control<boolean>(false, { validators: [Validators.requiredTrue] }),
    });

    this.formGroup.controls.disabilityCertificateVerified.valueChanges.subscribe((value) => {
      this.consentStore.setDisabilityCertificateVerified(!!value);
    });
  }
}
