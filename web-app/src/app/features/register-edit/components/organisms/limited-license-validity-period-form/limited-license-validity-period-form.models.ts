import { FormControl, FormGroup } from '@angular/forms';

export type ValidityPeriodType = 'limited' | 'unlimited';

export type LimitedLicenseValidityPeriodForm = {
  /**
   * Beginning of the validity period, which is always today's date in this case.
   */
  validFrom: FormControl<string>;
  /**
   * End of the validity period.
   */
  validTo: FormControl<string | null>;
  /**
   * Indicates whether the license is valid for a limited period.
   */
  validityPeriodType: FormControl<ValidityPeriodType | null>;
};

export type LimitedLicenseValidityPeriodFormGroup = FormGroup<LimitedLicenseValidityPeriodForm>;

export type LimitedLicenseValidityPeriodFormValues = LimitedLicenseValidityPeriodFormGroup['value'];
