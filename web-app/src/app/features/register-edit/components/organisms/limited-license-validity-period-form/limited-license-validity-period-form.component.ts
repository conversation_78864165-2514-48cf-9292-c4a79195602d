import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, computed, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { Observable, forkJoin, map, startWith } from 'rxjs';

import { LicenseType } from '@digifischdok/ngx-register-sdk';

import { LicenseInformationStore } from '@/app/core/stores/license-information.store';
import { PeriodRadioItemComponent } from '@/app/features/register-edit/components/molecules/period-radio-item/period-radio-item.component';
import {
  LimitedLicenseValidityPeriodFormGroup,
  ValidityPeriodType,
} from '@/app/features/register-edit/components/organisms/limited-license-validity-period-form/limited-license-validity-period-form.models';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';
import { DateValidators } from '@/app/shared/validators/date.validators';

@Component({
  selector: 'fish-limited-license-validity-period-form',
  standalone: true,
  imports: [PeriodRadioItemComponent, TranslateModule, NgClass],
  templateUrl: './limited-license-validity-period-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseValidityPeriodFormComponent extends FormComponent<LimitedLicenseValidityPeriodFormGroup> implements OnInit {
  // FIELDS
  public override formGroup!: LimitedLicenseValidityPeriodFormGroup;

  protected validToErrorMapping$!: Observable<ValidationErrorMapping>;

  protected readonly disabled = toComputed(() => {
    return this.formGroup.statusChanges.pipe(
      startWith(true),
      map(() => this.formGroup.disabled)
    );
  });

  protected readonly isTimeLimitable = computed<boolean>(() => {
    return this.licenseInformationStore.getLicenseConfiguration(LicenseType.Limited).isTimeLimitable;
  });

  // DEPENDENCIES
  private readonly formBuilder = inject(FormBuilder);

  private readonly licenseInformationStore = inject(LicenseInformationStore);

  public override ngOnInit(): void {
    this.initFormGroup();
  }

  private initFormGroup() {
    this.formGroup = this.formBuilder.group({
      validFrom: this.formBuilder.nonNullable.control<string>(new Date().toISOString().substring(0, 10)),
      validTo: this.formBuilder.control<string | null>(null),
      validityPeriodType: this.formBuilder.control<ValidityPeriodType | null>(null, { validators: [Validators.required] }),
    });

    this.formGroup.controls.validityPeriodType.statusChanges.subscribe(() => {
      if (this.formGroup.controls.validityPeriodType.value === 'limited') {
        this.formGroup.controls.validTo.enable();
        this.formGroup.controls.validTo.setValidators([Validators.required, DateValidators.notInPast(false)]);
      } else {
        this.formGroup.controls.validTo.disable();
        this.formGroup.controls.validTo.setValue(null);
        this.formGroup.controls.validTo.clearValidators();
      }
    });

    this.validToErrorMapping$ = forkJoin([this.translateService.get('common.form.error.date.not_in_past')]).pipe(
      map(([notInPastErrorMessage]) => ({
        notInPast: notInPastErrorMessage,
      }))
    );
  }
}
