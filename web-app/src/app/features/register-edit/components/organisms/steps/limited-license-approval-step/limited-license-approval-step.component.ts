import { AfterViewInit, ChangeDetectionStrategy, Component, input, output, viewChild } from '@angular/core';

import { BehaviorSubject, startWith } from 'rxjs';

import { LimitedLicenseApprovalDocumentPreviewBoxComponent } from '@/app/features/register-edit/components/molecules/limited-license-approval-document-preview-box/limited-license-approval-document-preview-box.component';
import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { LimitedLicenseApprovalFormComponent } from '@/app/features/register-edit/components/organisms/limited-license-approval-form/limited-license-approval-form.component';
import { LimitedLicenseApprovalFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-approval-form/limited-license-approval-form.models';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';

@Component({
  selector: 'fish-limited-license-approval-step',
  standalone: true,
  imports: [LimitedLicenseApprovalFormComponent, LimitedLicenseApprovalDocumentPreviewBoxComponent, EditFooterComponent],
  templateUrl: './limited-license-approval-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseApprovalStepComponent implements EditFormStep<LimitedLicenseApprovalFormGroup>, AfterViewInit {
  // Inputs
  public readonly isLoading = input<boolean>(false);

  public readonly requireJustificationForLimitedDurationNotice = input<boolean>(false);

  // Outputs
  public readonly saveButtonClicked = output<void>();

  public readonly backButtonClicked = output<void>();

  // Fields
  public canContinue$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  public formGroup!: LimitedLicenseApprovalFormGroup;

  private readonly limitedLicenseApprovalForm = viewChild.required(LimitedLicenseApprovalFormComponent);

  public ngAfterViewInit(): void {
    this.formGroup = this.limitedLicenseApprovalForm().formGroup;
    this.formGroup.statusChanges.pipe(startWith(this.formGroup.valid)).subscribe(() => {
      this.canContinue$.next(this.formGroup.valid);
    });
  }

  public onContinue(): void {
    this.limitedLicenseApprovalForm().validate();
    if (this.formGroup.valid) {
      this.saveButtonClicked.emit();
    }
  }
}
