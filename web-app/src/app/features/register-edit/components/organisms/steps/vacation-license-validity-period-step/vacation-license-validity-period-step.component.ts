import { AfterViewInit, ChangeDetectionStrategy, Component, OutputEmitterRef, ViewChild, computed, inject, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, startWith } from 'rxjs';

import { FishingLicense, LicenseType } from '@digifischdok/ngx-register-sdk';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { VacationLicenseValidityPeriodStepFormGroup } from '@/app/features/register-edit/components/organisms/steps/vacation-license-validity-period-step/vacation-license-validity-period-step.models';
import { VacationLicenseValidityPeriodFormComponent } from '@/app/features/register-edit/components/organisms/vacation-license-validity-period-form/vacation-license-validity-period-form.component';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';

@Component({
  selector: 'fish-vacation-license-validity-period-step',
  standalone: true,
  imports: [
    EditFooterComponent,
    VacationLicenseValidityPeriodFormComponent,
    CardComponent,
    CardHeaderComponent,
    CardContentComponent,
    TranslateModule,
  ],
  templateUrl: './vacation-license-validity-period-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VacationLicenseValidityPeriodStepComponent implements EditFormStep<VacationLicenseValidityPeriodStepFormGroup>, AfterViewInit {
  // DEPENDENCIES
  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  // INPUTS
  public readonly vacationLicenseToExtendNumber = input<string>(); // for a vacation license extension instead of creation

  // OUTPUTS
  public continueButtonClicked: OutputEmitterRef<void> = output();

  public backButtonClicked: OutputEmitterRef<void> = output();

  // FIELDS
  public readonly canContinue$ = new BehaviorSubject<boolean>(false); // always true for this step

  public formGroup!: VacationLicenseValidityPeriodStepFormGroup;

  @ViewChild(VacationLicenseValidityPeriodFormComponent)
  public formComponent!: VacationLicenseValidityPeriodFormComponent;

  protected readonly vacationLicenseToExtend = computed<FishingLicense | undefined>(() => {
    return this.citizenStore.profile()?.fishingLicenses?.find((license) => license.number === this.vacationLicenseToExtendNumber());
  });

  protected readonly vacationLicenses = computed<FishingLicense[] | undefined>(() => {
    return this.citizenStore.profile()?.fishingLicenses?.filter((license) => license.type === LicenseType.Vacation);
  });

  public ngAfterViewInit(): void {
    this.formGroup = this.formComponent.formGroup;

    this.formGroup.statusChanges.pipe(startWith(this.formGroup.valid)).subscribe(() => {
      this.canContinue$.next(this.formGroup.valid);
    });
  }

  protected onContinue(): void {
    this.formComponent.validate();
    if (this.formGroup.valid) {
      this.continueButtonClicked.emit();
    }
  }
}
