import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild, computed, inject, input, output, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, distinctUntilChanged, startWith } from 'rxjs';

import { LicenseDurationType, LicenseType } from '@digifischdok/ngx-register-sdk';

import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { PaymentBoxComponent } from '@/app/features/register-edit/components/organisms/payment-box/payment-box.component';
import { PaymentMethodCardComponent } from '@/app/features/register-edit/components/organisms/payment-method-card/payment-method-card.component';
import { ReadonlyPaymentBoxComponent } from '@/app/features/register-edit/components/organisms/readonly-payment-box/readonly-payment-box.component';
import { PaymentsStepFormGroup } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.models';
import { PaymentStepFormValidator } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.validators';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { TaxSelection } from '@/app/shared/models/tax-selection';
import { CostsService } from '@/app/shared/services/costs/costs.service';

@Component({
  selector: 'fish-payments-step',
  standalone: true,
  imports: [EditFooterComponent, TranslateModule, PaymentBoxComponent, PaymentMethodCardComponent, ReadonlyPaymentBoxComponent],
  templateUrl: './payments-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentsStepComponent implements EditFormStep<PaymentsStepFormGroup>, AfterViewInit {
  // Dependencies
  private readonly costsService: CostsService = inject(CostsService);

  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  // Inputs
  public readonly showFee = input<boolean>(true);

  public readonly isTaxOptional = input<boolean>(true);

  public readonly isTaxReadonly = input<boolean>(false);

  public readonly showPreviouslyPaidTax = input<boolean>(false);

  public readonly licenseType = input<LicenseType>(LicenseType.Regular);

  public readonly previouslyPaid = input<TaxSelection | null>(null);

  public readonly isLoading = input<boolean>(false);

  public readonly isLastStep = input<boolean>(true);

  public readonly showBackButton = input<boolean>(true);

  public readonly isTaxCheckboxDisabled = input<boolean>(false);

  public readonly continueButtonLabel = input<string>();

  public readonly selectedValidityPeriod = input<
    | {
        validFrom: string;
        validTo: string;
      }
    | undefined
  >(undefined);

  public readonly selectedDuration = input<number | undefined>();

  // Outputs
  public readonly saveButtonClicked = output<void>();

  public readonly continueButtonClicked = output<void>();

  public readonly backButtonClicked = output<void>();

  // Fields
  public canContinue$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  public formGroup!: PaymentsStepFormGroup;

  protected readonly taxCostOptions = toSignal(this.costsService.getTaxOptions$(), { initialValue: [] });

  protected readonly feeCost = computed(() => this.costsService.calculateLicenseFee(this.selectedDuration(), this.licenseType()));

  private readonly taxSelection = signal<TaxSelection | null>(null);

  protected readonly isPaymentMethodRequired = computed<boolean>(() => {
    if (this.isTaxOptional() && !this.showFee()) {
      return this.taxSelection() !== null;
    }
    return true;
  });

  @ViewChild(PaymentBoxComponent)
  private readonly regularPaymentBox?: PaymentBoxComponent;

  @ViewChild(ReadonlyPaymentBoxComponent)
  private readonly readonlyPaymentBox?: ReadonlyPaymentBoxComponent;

  @ViewChild(PaymentMethodCardComponent)
  private readonly paymentMethodCard!: PaymentMethodCardComponent;

  protected get paymentBox(): PaymentBoxComponent | ReadonlyPaymentBoxComponent {
    return this.readonlyPaymentBox ?? this.regularPaymentBox!;
  }

  public ngAfterViewInit(): void {
    this.formGroup = this.formBuilder.group(
      {
        taxSelection: this.paymentBox.formGroup.controls.taxSelection,
        paymentMethod: this.paymentMethodCard.formGroup.controls.paymentMethod,
      },
      { validators: PaymentStepFormValidator(this.isTaxOptional(), this.showFee()) }
    );

    this.formGroup.statusChanges
      .pipe(startWith(this.formGroup.valid), distinctUntilChanged())
      .subscribe(() => this.canContinue$.next(this.formGroup.valid));
    this.taxSelection.set(this.formGroup.controls.taxSelection.value);
    this.formGroup.controls.taxSelection.valueChanges.subscribe(() => {
      this.taxSelection.set(this.formGroup.controls.taxSelection.value);
    });
    this.formGroup.updateValueAndValidity({ emitEvent: true });
  }

  public onContinue(): void {
    this.paymentBox.validate();
    this.paymentMethodCard.validate();
    if (this.formGroup.valid) {
      this.continueButtonClicked.emit();
      this.saveButtonClicked.emit();
    }
  }

  protected readonly LicenseDurationType = LicenseDurationType;
}
