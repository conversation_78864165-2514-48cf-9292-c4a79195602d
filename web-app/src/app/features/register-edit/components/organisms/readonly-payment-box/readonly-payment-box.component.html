<fish-card>
  <fish-card-header [cost]="totalAmount()" class="text-l" variant="primary" data-testid="payment-box-cost">
    <fish-icon-money icon size="64"></fish-icon-money>
    <span [innerText]="'edit_form.payments.box_title' | translate"></span>
  </fish-card-header>

  <fish-card-content>
    <div class="flex flex-col items-stretch gap-4">
      @if (showFee()) {
        <fish-fee-payment-item
          [amount]="feeCost()"
          [licenseType]="licenseType()"
          [validTo]="selectedValidityPeriod()?.validTo"
          data-testid="payment-box-fee"
        ></fish-fee-payment-item>
      }

      @if (previouslyPaidTax() !== null) {
        <fish-payed-taxes-payment-item
          [yearFrom]="previouslyPaidTax()?.yearFrom ?? null"
          [yearTo]="previouslyPaidTax()?.yearTo ?? null"
          data-testid="payment-box-payed-taxes"
        />
      }
      @if (formGroup.controls.taxSelection.value) {
        <fish-readonly-tax-payment-item
          [taxSelection]="formGroup.controls.taxSelection.value"
          data-testid="payment-box-readonly-tax-payment-item"
        ></fish-readonly-tax-payment-item>
      }
    </div>
  </fish-card-content>
</fish-card>
