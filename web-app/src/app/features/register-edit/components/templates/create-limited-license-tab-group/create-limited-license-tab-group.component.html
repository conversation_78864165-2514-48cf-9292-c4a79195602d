<fish-tab-group>
  <fish-tab [disabled]="0 | tabDisabled: lastEditableIndex() : formSent()" [label]="'edit_form.tab_titles.personal_data' | translate">
    @if (!formSent()) {
      <fish-personal-data-step (continueButtonClicked)="tabGroup().goNext()"></fish-personal-data-step>
    }
  </fish-tab>
  <fish-tab [disabled]="1 | tabDisabled: lastEditableIndex() : formSent()" [label]="'edit_form.tab_titles.limited_license_proof' | translate">
    @if (!formSent()) {
      <fish-limited-license-qualification-proof-step
        (continueButtonClicked)="tabGroup().goNext()"
        (backButtonClicked)="tabGroup().goBack()"
      ></fish-limited-license-qualification-proof-step>
    }
  </fish-tab>
  <fish-tab [disabled]="2 | tabDisabled: lastEditableIndex() : formSent()" [label]="'edit_form.tab_titles.payments' | translate">
    @if (!formSent()) {
      <fish-payments-step
        (backButtonClicked)="tabGroup().goBack()"
        (continueButtonClicked)="tabGroup().goNext()"
        [licenseType]="LicenseType.Limited"
        [isLastStep]="false"
        [showFee]="true"
        [selectedValidityPeriod]="selectedValidityPeriod()"
      >
      </fish-payments-step>
    }
  </fish-tab>
  <fish-tab [disabled]="3 | tabDisabled: lastEditableIndex() : formSent()" [label]="'edit_form.tab_titles.limited_license_approval' | translate">
    @if (!formSent()) {
      <fish-limited-license-approval-step
        (backButtonClicked)="tabGroup().goBack()"
        (saveButtonClicked)="handleSave()"
        [isLoading]="isLoading()"
        [requireJustificationForLimitedDurationNotice]="!!isLicenseValidityLimited()"
      ></fish-limited-license-approval-step>
    }
  </fish-tab>
  <fish-tab [disabled]="!formSent()" [label]="'edit_form.tab_titles.documents' | translate" section="documents">
    <fish-documents-step [documents]="documents()" [registerEntryId]="registerEntryId()!" [showLicenseCardBox]="false"></fish-documents-step>
  </fish-tab>
</fish-tab-group>
