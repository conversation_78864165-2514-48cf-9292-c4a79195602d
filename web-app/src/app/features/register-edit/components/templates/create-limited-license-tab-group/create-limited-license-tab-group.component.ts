import { AfterViewInit, ChangeDetectionStrategy, Component, OnD<PERSON>roy, computed, inject, isDevMode, signal, viewChild } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, combineLatest, finalize, map } from 'rxjs';

import {
  CreateFishingLicenseResponse,
  CreateLimitedFishingLicenseRequest,
  FishingLicenseService,
  IdentificationDocument,
  LicenseType,
} from '@digifischdok/ngx-register-sdk';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { ConsentStore } from '@/app/core/stores/consent.store';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { DocumentsStepComponent } from '@/app/features/register-edit/components/organisms/documents-step/documents-step.component';
import { LimitedLicenseApprovalStepComponent } from '@/app/features/register-edit/components/organisms/steps/limited-license-approval-step/limited-license-approval-step.component';
import { LimitedLicenseQualificationProofStepComponent } from '@/app/features/register-edit/components/organisms/steps/limited-license-qualification-proof-step/limited-license-qualification-proof-step.component';
import { PaymentsStepComponent } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.component';
import { PersonalDataStepComponent } from '@/app/features/register-edit/components/organisms/steps/personal-data-step/personal-data-step.component';
import { TabDisabledPipe } from '@/app/features/register-edit/pipes/tab-disabled.pipe';
import { LimitedLicenseApprovalBuilderService } from '@/app/features/register-edit/services/limited-license-approval-builder.service';
import { RequestPaymentsBuilderService } from '@/app/features/register-edit/services/request-payments-builder.service';
import { RequestPersonBuilderService } from '@/app/features/register-edit/services/request-person-builder.service';
import { RequestValidityPeriodBuilderService } from '@/app/features/register-edit/services/request-validity-period-builder.service';
import { TabComponent } from '@/app/shared/atoms/tab/tab.component';
import { TabGroupComponent } from '@/app/shared/molecules/tab-group/tab-group.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-create-limited-license-tab-group',
  standalone: true,
  imports: [
    TabGroupComponent,
    TabComponent,
    TabDisabledPipe,
    PersonalDataStepComponent,
    PaymentsStepComponent,
    TranslateModule,
    DocumentsStepComponent,
    LimitedLicenseQualificationProofStepComponent,
    LimitedLicenseApprovalStepComponent,
  ],
  templateUrl: './create-limited-license-tab-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateLimitedLicenseTabGroupComponent implements AfterViewInit, OnDestroy {
  // Dependencies
  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  private readonly profileHeaderStore: ProfileHeaderStore = inject(ProfileHeaderStore);

  private readonly consentStore: ConsentStore = inject(ConsentStore);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  private readonly licenseService: FishingLicenseService = inject(FishingLicenseService);

  private readonly personBuilder: RequestPersonBuilderService = inject(RequestPersonBuilderService);

  private readonly paymentsBuilder: RequestPaymentsBuilderService = inject(RequestPaymentsBuilderService);

  private readonly validityPeriodBuilder: RequestValidityPeriodBuilderService = inject(RequestValidityPeriodBuilderService);

  private readonly approvalBuilder: LimitedLicenseApprovalBuilderService = inject(LimitedLicenseApprovalBuilderService);

  // Fields
  protected readonly isLoading = signal<boolean>(false);

  protected readonly lastEditableIndex = signal<number>(0);

  protected readonly formSent = signal<boolean>(false);

  protected readonly registerEntryId = computed(() => this.citizenStore.profile()?.registerId);

  protected readonly documents = signal<IdentificationDocument[]>([]);

  protected readonly isLicenseValidityLimited = toComputed(() => {
    const control = this.qualificationProofStep().formGroup.controls.limitedLicenseValidityPeriod.controls.validityPeriodType;
    return control.statusChanges.pipe(map(() => control.value === 'limited'));
  });

  protected readonly selectedValidityPeriod = toComputed(() => {
    const { validFrom, validTo, validityPeriodType } = this.qualificationProofStep().formGroup.controls.limitedLicenseValidityPeriod.controls;
    return combineLatest([validFrom.valueChanges, validTo.valueChanges, validityPeriodType.valueChanges]).pipe(
      map(() => {
        if (validityPeriodType.value === 'limited') {
          return {
            validFrom: validFrom.value!,
            validTo: validTo.value!,
          };
        }
        return undefined;
      })
    );
  });

  protected readonly tabGroup = viewChild.required(TabGroupComponent);

  private readonly personalDataStep = viewChild.required(PersonalDataStepComponent);

  private readonly qualificationProofStep = viewChild.required(LimitedLicenseQualificationProofStepComponent);

  private readonly paymentsStep = viewChild.required(PaymentsStepComponent);

  private readonly approvalStep = viewChild.required(LimitedLicenseApprovalStepComponent);

  public ngAfterViewInit(): void {
    this.setLastEditableIndex();
  }

  public ngOnDestroy(): void {
    this.profileHeaderStore.setHomeButtonType('secondary');
  }

  protected handleSave(): void {
    this.isLoading.set(true);
    const registerEntryId = this.registerEntryId();
    const request = this.buildCreateLimitedFishingLicenseRequest();
    (registerEntryId
      ? this.licenseService.fishingLicenseControllerCreateLimited(registerEntryId, request)
      : this.licenseService.fishingLicenseRootControllerCreateLimited(request)
    )
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isLoading.set(false))
      )
      .subscribe((response) => this.handleSuccessfulResponse(response));
  }

  private buildCreateLimitedFishingLicenseRequest(): CreateLimitedFishingLicenseRequest {
    return {
      person: this.personBuilder.buildPerson(this.personalDataStep().formGroup.value),
      fees: this.paymentsBuilder.buildFees(
        this.LicenseType.Limited,
        this.paymentsStep().formGroup.value,
        this.qualificationProofStep().formGroup.controls.limitedLicenseValidityPeriod.value
      ),
      taxes: this.paymentsBuilder.buildTaxes(this.paymentsStep().formGroup.value),
      validityPeriod: this.validityPeriodBuilder.buildValidityPeriod(
        this.qualificationProofStep().formGroup.controls.limitedLicenseValidityPeriod.value
      ),
      consentInfo: this.consentStore.getLimitedLicenseConsentInfo(),
      limitedLicenseApproval: this.approvalBuilder.buildLimitedLicenseApproval(this.approvalStep().formGroup.value),
    };
  }

  private handleSuccessfulResponse(response: CreateFishingLicenseResponse): void {
    const registerEntryId = response.registerEntryId;
    if (!registerEntryId) {
      throw new Error('Inconsistent server state: registerEntryId is null');
    }
    const documents = response.documents;
    if (documents?.length) {
      this.documents.set(documents);
    }

    // Ensure update when profile header is set
    this.citizenStore.patchByResponse(response);
    this.profileHeaderStore.setHomeButtonType('primary');
    this.formSent.set(true);

    // Wait for thread complete
    setTimeout(() => this.tabGroup().goToLastTab(), 1);
  }

  private setLastEditableIndex(): void {
    if (isDevMode()) {
      this.lastEditableIndex.set(3);
    } else {
      combineLatest([
        this.personalDataStep().canContinue$,
        this.qualificationProofStep().canContinue$,
        this.paymentsStep().canContinue$,
        this.approvalStep().canContinue$,
      ]).subscribe((arr) => {
        const latestValid = arr.lastIndexOf(true);
        const firstInvalid = arr.indexOf(false);

        const lastEditable = firstInvalid >= 0 ? firstInvalid : latestValid + 1;

        this.lastEditableIndex.set(lastEditable);
      });
    }
  }

  protected readonly LicenseType = LicenseType;
}
