import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { UserRole } from '@/app/core/services/user/user.constants';
import { ButtonMenuContentComponent } from '@/app/shared/atoms/button-menu-content/button-menu-content.component';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconFishingTaxComponent } from '@/app/shared/icons/fisching-tax/fishing-tax.component';
import { IconHandicappedComponent } from '@/app/shared/icons/handicapped/handicapped.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IconPlusComponent } from '@/app/shared/icons/plus/plus.component';
import { IconVacationComponent } from '@/app/shared/icons/vacation/vacation.component';
import { ButtonMenuComponent } from '@/app/shared/molecules/button-menu/button-menu.component';

@Component({
  selector: 'fish-creation-menu',
  standalone: true,
  imports: [
    ButtonMenuComponent,
    ButtonMenuContentComponent,
    ButtonComponent,
    IconLicenseCardComponent,
    TranslateModule,
    IconVacationComponent,
    IconFishingTaxComponent,
    IconHandicappedComponent,
    IconPlusComponent,
  ],
  templateUrl: './creation-menu.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreationMenuComponent {
  // Fields
  protected readonly showCreateLimitedLicenseButton = computed<boolean>(() => {
    return this.keycloak.getKeycloakInstance().hasRealmRole(UserRole.LimitedLicenseCreator);
  });

  // Dependencies
  private readonly keycloak = inject(KeycloakService);
}
