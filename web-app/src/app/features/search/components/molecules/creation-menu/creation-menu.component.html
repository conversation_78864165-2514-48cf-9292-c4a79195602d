<fish-button-menu data-testid="Creation-menu" [placeholder]="'search.creation_menu.placeholder' | translate">
  <fish-icon-plus size="32" open-icon />

  <fish-button-menu-content>
    <fish-button data-testid="creation-menu-license" size="m" type="secondary" [routeTo]="'/digitize-license'">
      <fish-icon-license-card size="32" icon />
      {{ 'search.creation_menu.license_button' | translate }}
    </fish-button>
    <fish-button size="m" type="secondary" data-testid="creation-menu-vacation" [routeTo]="'/create-vacation-license'">
      <fish-icon-vacation size="32" icon />
      {{ 'search.creation_menu.vacation_button' | translate }}
    </fish-button>
    <fish-button data-testid="creation-menu-tax" size="m" type="secondary" [routeTo]="'/create-person'">
      <fish-icon-fishing-tax size="32" icon />
      {{ 'search.creation_menu.tax_button' | translate }}
    </fish-button>
    @if (showCreateLimitedLicenseButton()) {
      <fish-button data-testid="creation-menu-special" size="m" type="secondary" [routeTo]="'/create-limited-license'">
        <fish-icon-handicapped size="32" icon />
        {{ 'search.creation_menu.special_button' | translate }}
      </fish-button>
    }
  </fish-button-menu-content>
</fish-button-menu>
