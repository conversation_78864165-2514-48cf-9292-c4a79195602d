<fish-service-card>
  <fish-service-card-header>
    <fish-icon-license-card icon size="96" />
    <span [innerText]="'service_overview.license_card.title' | translate"></span>
  </fish-service-card-header>
  <fish-service-card-content>
    @if (showNoLicenses()) {
      <div class="flex h-full w-full flex-col items-center justify-center gap-2">
        <fish-icon-no-license-card size="96" />
        <fish-badge type="primary">
          <span [innerText]="'service_overview.license_card.no_licenses' | translate"></span>
        </fish-badge>
      </div>
    } @else {
      <div class="flex w-full flex-col items-stretch gap-6">
        @if (showCertificate()) {
          <fish-license-card-certificate-item [certificate]="certificate()!"></fish-license-card-certificate-item>
        }
        @for (license of filteredLicenses(); track license.number) {
          <fish-license-card-license-item [license]="license"></fish-license-card-license-item>
        }
      </div>
    }
  </fish-service-card-content>
  <fish-service-card-footer [showCreationMenu]="showCreationMenu()" [creationMenuButtonOnly]="creationMenuButtonOnly()">
    <fish-license-creation-menu
      [certificate]="certificate()"
      [jurisdictionIsMatching]="isJurisdictionMatching()"
      [jurisdiction]="jurisdiction()"
      menu-items
    />
    <ng-template fishServiceCardButton>
      @if (regularLicense()) {
        <fish-inspection-link
          [routeTo]="['order-card', regularLicense()?.number]"
          [disabled]="!isJurisdictionMatching()"
          data-testid="service-overview-license-card-reorder"
        >
          <span [innerText]="'service_overview.license_card.button.reorder' | translate"></span>
        </fish-inspection-link>
      } @else if (extendableVacationLicense()) {
        <fish-inspection-link
          [routeTo]="['extend-vacation-license', extendableVacationLicense()?.number]"
          data-testid="service-overview-license-card-extend"
        >
          <span [innerText]="'service_overview.license_card.button.extend' | translate"></span>
        </fish-inspection-link>
      }
    </ng-template>
  </fish-service-card-footer>
</fish-service-card>
