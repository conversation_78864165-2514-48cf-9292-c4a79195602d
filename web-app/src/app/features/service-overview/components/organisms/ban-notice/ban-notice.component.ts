import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OutputEmitterRef, computed, inject, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { Ban } from '@digifischdok/ngx-register-sdk';

import { UserRole } from '@/app/core/services/user/user.constants';
import { UserService } from '@/app/core/services/user/user.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { NoticeActionsComponent } from '@/app/shared/atoms/notice-actions/notice-actions.component';
import { NoticeContentComponent } from '@/app/shared/atoms/notice-content/notice-content.component';
import { IconEditComponent } from '@/app/shared/icons/edit/edit.component';
import { IconUnlockComponent } from '@/app/shared/icons/unlock/unlock.component';
import { NoticeComponent } from '@/app/shared/molecules/notice/notice.component';

@Component({
  selector: 'fish-ban-notice',
  standalone: true,
  imports: [
    ButtonComponent,
    NoticeActionsComponent,
    NoticeComponent,
    NoticeContentComponent,
    TranslateModule,
    DatePipe,
    IconEditComponent,
    IconUnlockComponent,
  ],
  templateUrl: './ban-notice.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BanNoticeComponent {
  // Outputs
  public readonly deleteButtonClicked: OutputEmitterRef<void> = output<void>();

  // Dependencies
  private readonly userService: UserService = inject(UserService);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  private readonly keycloakService: KeycloakService = inject(KeycloakService);

  // Fields
  protected readonly ban = computed<Ban | undefined>(() => {
    return this.citizenStore.profile()?.ban;
  });

  protected readonly isJurisdictionMatching = computed<boolean>(() => {
    const registerState = this.citizenStore.profile()?.jurisdiction?.federalState;
    return registerState != null && this.userService.getFederalState() === registerState;
  });

  protected readonly hasAuthorityToManageBans = computed<boolean>(() => {
    return this.keycloakService.getKeycloakInstance().hasRealmRole(UserRole.BanManager) && this.isJurisdictionMatching();
  });
}
