<div class="flex flex-col items-start gap-4">
  @if (certificate()) {
    <fish-button
      [disabled]="isAddRegularLicenseDisabled()"
      size="m"
      type="secondary"
      [routeTo]="'add-license'"
      data-testid="service-overview-license-card-create-license"
    >
      <fish-icon-license-card size="32" icon />
      <span [innerText]="'service_overview.license_card.button.create_license' | translate"></span>
    </fish-button>
  } @else {
    <fish-button [disabled]="!jurisdictionIsMatching()" size="m" type="secondary" data-testid="service-overview-license-card-digitize-license">
      <fish-icon-license-card size="32" icon />
      <span [innerText]="'service_overview.license_card.button.digitize' | translate"></span>
    </fish-button>
  }
  <fish-button [routeTo]="'add-vacation-license'" size="m" type="secondary" data-testid="service-overview-license-card-vacation-license">
    <fish-icon-vacation icon size="32" />
    <span [innerText]="'service_overview.license_card.button.create_vacation_license' | translate"></span>
  </fish-button>

  @if (showAddLimitedLicenseButton()) {
    <fish-button
      [routeTo]="'add-limited-license'"
      [disabled]="!jurisdictionIsMatching()"
      size="m"
      type="secondary"
      data-testid="service-overview-license-card-limited-license"
    >
      <fish-icon-handicapped icon size="32" />
      <span [innerText]="'service_overview.license_card.button.create_limited_license' | translate"></span>
    </fish-button>
  }
</div>
