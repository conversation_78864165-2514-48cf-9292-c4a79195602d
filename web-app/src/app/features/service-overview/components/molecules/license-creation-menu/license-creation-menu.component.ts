import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { Jurisdiction, QualificationsProof } from '@digifischdok/ngx-register-sdk';

import { UserRole } from '@/app/core/services/user/user.constants';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconHandicappedComponent } from '@/app/shared/icons/handicapped/handicapped.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IconVacationComponent } from '@/app/shared/icons/vacation/vacation.component';

@Component({
  selector: 'fish-license-creation-menu',
  standalone: true,
  imports: [ButtonComponent, IconLicenseCardComponent, IconVacationComponent, TranslateModule, IconHandicappedComponent],
  templateUrl: './license-creation-menu.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LicenseCreationMenuComponent {
  // Inputs
  public readonly certificate = input<QualificationsProof | undefined>();

  public readonly jurisdiction = input.required<Jurisdiction>();

  // Whether the jurisdiction of the register entry matches with the federal state of the logged-in user
  public readonly jurisdictionIsMatching = input.required<boolean>();

  // Fields
  public readonly isAddRegularLicenseDisabled = computed(() => !!this.jurisdiction() && !this.jurisdictionIsMatching());

  protected readonly showAddLimitedLicenseButton = computed<boolean>(() => {
    return this.keycloak.getKeycloakInstance().hasRealmRole(UserRole.LimitedLicenseCreator);
  });

  // Dependencies
  private readonly keycloak = inject(KeycloakService);
}
