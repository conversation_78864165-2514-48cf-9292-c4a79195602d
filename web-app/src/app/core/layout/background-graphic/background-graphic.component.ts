import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { twMerge } from 'tailwind-merge';

import { BackgroundOpacity, IWavePatternConfig } from '@/app/core/layout/background-graphic/background-graphic.models';
import { BackgroundWavePatternComponent } from '@/app/core/layout/background-wave-pattern/background-wave-pattern.component';

@Component({
  standalone: true,
  selector: 'fish-background-graphic',
  templateUrl: './background-graphic.component.html',
  imports: [CommonModule, BackgroundWavePatternComponent, TranslateModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BackgroundGraphicComponent {
  @Input() public opacity: BackgroundOpacity = 'faded';

  @HostBinding('class')
  public get hostClass(): string {
    return twMerge('fixed bottom-0 -z-10 w-full');
  }

  protected get waves(): IWavePatternConfig[] {
    return [0, 1, 2, 3, 4, 5, 6, 7].map((index) => ({
      bottom: `${36 * index}px`,
      zIndex: -20 - index,
    }));
  }

  protected get _opacity(): number {
    switch (this.opacity) {
      case 'emphasized':
        return 0.5;
      case 'faded':
        return 0.2;
    }
  }
}
