# Stage 1, "build-stage", based on Node.js, to build and compile the frontend
ARG DOCKER_REGISTRY_URL=docker.io
FROM ${DOCKER_REGISTRY_URL}/node:23.6.1-alpine3.20 AS build-stage
WORKDIR /app
COPY . .
RUN npm ci
ARG configuration=production
RUN npm run build -- --output-path=./dist/out --configuration $configuration

# Stage 2, "production-stage", based on Nginx, to serve the compiled frontend
FROM ${DOCKER_REGISTRY_URL}/nginxinc/nginx-unprivileged:1.26-alpine

COPY --from=build-stage /app/dist/out/browser /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 8080
